# Glob Tool Implementation

## 概述

本文档描述了 `packages/haicode/src/tools/glob.ts` 的实现，该实现基于 `packages/core/src/tools/glob.ts` 的核心逻辑，但遵循 langchainjs 的 tool 定义规范。

## 核心特性

### 1. 与 packages/core 的逻辑一致性

- **文件排序**: 实现了相同的文件排序逻辑，优先显示最近修改的文件（24小时内），然后按字母顺序排列
- **路径处理**: 包含了相同的路径工具函数（`makeRelative`, `shortenPath`, `isWithinRoot`）
- **Git 忽略**: 支持 `.gitignore` 模式的文件过滤
- **参数验证**: 实现了相同的参数验证逻辑

### 2. LangChain.js 兼容性

- **Tool 定义**: 使用 `tool()` 函数创建，符合 langchainjs 规范
- **Schema 验证**: 使用 Zod 进行参数 schema 定义
- **错误处理**: 遵循 langchainjs 的错误处理模式
- **类型安全**: 完全类型化，避免使用 `any` 和 `unknown`

## 主要组件

### 接口定义

```typescript
export interface GlobPath {
  fullpath(): string;
  mtimeMs?: number;
}

export interface GlobToolParams {
  pattern: string;
  path?: string | null;
  case_sensitive?: boolean | null;
  respect_git_ignore?: boolean | null;
}
```

### 核心函数

1. **sortFileEntries**: 文件排序逻辑
2. **isWithinRoot**: 路径安全检查
3. **makeRelative**: 相对路径计算
4. **shortenPath**: 路径缩短显示
5. **SimpleFileFilteringService**: 简化的文件过滤服务

### Tool 实现

```typescript
export const globTool = tool(
  async (params: GlobToolParams) => {
    // 实现逻辑
  },
  {
    name: "glob",
    description: "...",
    schema: z.object({
      pattern: z.string().describe("..."),
      path: z.string().nullable().optional().describe("..."),
      case_sensitive: z.boolean().nullable().optional().describe("..."),
      respect_git_ignore: z.boolean().nullable().optional().describe("..."),
    }),
  }
);
```

## 功能特性

### 1. 模式匹配

- 支持标准 glob 模式（`**/*.ts`, `*.json` 等）
- 支持大小写敏感/不敏感搜索
- 支持复合模式（`**/*.{json,md}`）

### 2. 路径处理

- 自动解析相对路径和绝对路径
- 安全检查，防止访问根目录外的文件
- 智能路径缩短显示

### 3. 文件过滤

- 默认忽略 `node_modules`, `.git` 等目录
- 支持自定义 git ignore 模式
- 可选择是否遵循 `.gitignore` 规则

### 4. 结果排序

- 最近修改的文件（24小时内）优先显示
- 相同时间范围内按修改时间倒序
- 其他文件按字母顺序排列

## 使用示例

```typescript
import { globTool } from './tools/glob.js';

// 查找所有 TypeScript 文件
const result = await globTool.invoke({ pattern: '**/*.ts' });

// 在特定目录中搜索
const srcFiles = await globTool.invoke({ 
  pattern: '*.ts',
  path: './src'
});

// 大小写敏感搜索
const upperCase = await globTool.invoke({ 
  pattern: '**/*.TS',
  case_sensitive: true
});

// 包含被 git 忽略的文件
const allFiles = await globTool.invoke({ 
  pattern: '**/*',
  respect_git_ignore: false
});
```

## 测试覆盖

实现包含完整的测试套件：

- 辅助函数测试（路径处理、排序等）
- 集成测试（实际文件搜索）
- 边界情况测试（空结果、错误参数等）
- 功能特性测试（大小写敏感、目录限制等）

## 性能考虑

- 使用流式 glob 搜索以处理大型目录
- 智能文件过滤减少不必要的文件系统访问
- 高效的排序算法处理大量文件结果

## 未来改进

1. 支持更复杂的 `.gitignore` 解析
2. 添加文件大小和类型过滤选项
3. 支持搜索结果缓存
4. 添加进度报告功能（对于大型搜索）
