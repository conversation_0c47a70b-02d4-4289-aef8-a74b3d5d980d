# MCP (Model Context Protocol) Integration

HaiCodeAgent 支持 MCP (Model Context Protocol) 集成，允许代理使用来自外部 MCP 服务器的工具，大大扩展其功能。

## 什么是 MCP？

Model Context Protocol (MCP) 是一个开放协议，标准化了应用程序如何为语言模型提供工具和上下文。通过 MCP，HaiCodeAgent 可以：

- 连接到多个 MCP 服务器
- 使用外部工具和服务
- 动态加载和管理工具
- 支持不同的传输协议（stdio、HTTP、SSE）

## 快速开始

### 基本配置

```typescript
import { HaiCodeAgent } from '@ht/hai-code-cli';

const agent = new HaiCodeAgent({
  mcp: {
    enabled: true,
    servers: {
      "time-server": {
        command: "uvx",
        args: ["mcp-server-time"],
        transport: "stdio",
      },
    },
  },
});

// 使用 MCP 工具
const response = await agent.processMessage("What time is it?");
console.log(response);

// 清理资源
await agent.close();
```

### 预定义服务器

HaiCodeAgent 提供了一些预定义的 MCP 服务器配置：

```typescript
import { PREDEFINED_MCP_SERVERS } from '@ht/hai-code-cli/config/mcp';

const agent = new HaiCodeAgent({
  mcp: {
    enabled: true,
    servers: {
      "time-server": PREDEFINED_MCP_SERVERS["time-server"],
      "browser-tools": PREDEFINED_MCP_SERVERS["browser-tools"],
      "filesystem": PREDEFINED_MCP_SERVERS["filesystem"],
    },
  },
});
```

## 配置选项

### MCP 服务器配置

#### Stdio 传输

```typescript
{
  command: "uvx",                    // 要执行的命令
  args: ["mcp-server-time"],         // 命令参数
  transport: "stdio",                // 传输类型
  cwd: "/path/to/working/dir",       // 工作目录（可选）
  env: {                             // 环境变量（可选）
    "PYTHONPATH": "/custom/path"
  },
  restart: {                         // 重启配置（可选）
    enabled: true,
    maxAttempts: 3,
    delayMs: 1000,
  },
}
```

#### HTTP/SSE 传输

```typescript
{
  url: "http://localhost:8000/sse",  // 服务器 URL
  transport: "sse",                  // 传输类型：http 或 sse
  headers: {                         // HTTP 头（可选）
    "Authorization": "Bearer token"
  },
  reconnect: {                       // 重连配置（可选）
    enabled: true,
    maxAttempts: 3,
    delayMs: 1000,
  },
}
```

### 全局设置

```typescript
{
  enabled: true,                     // 是否启用 MCP
  servers: { /* 服务器配置 */ },
  settings: {
    prefixToolNameWithServerName: true,    // 工具名前缀服务器名
    additionalToolNamePrefix: "mcp_",      // 额外的工具名前缀
    throwOnLoadError: false,               // 加载错误时是否抛出异常
    defaultToolTimeout: 30000,             // 默认工具超时时间（毫秒）
  },
}
```

## 使用示例

### 多服务器配置

```typescript
const agent = new HaiCodeAgent({
  mcp: {
    enabled: true,
    servers: {
      "time-server": {
        command: "uvx",
        args: ["mcp-server-time"],
        transport: "stdio",
      },
      "weather-api": {
        url: "http://localhost:8000/sse",
        transport: "sse",
        headers: {
          "API-Key": "your-api-key",
        },
      },
    },
    settings: {
      prefixToolNameWithServerName: true,
      throwOnLoadError: false,
    },
  },
});
```

### 动态配置更新

```typescript
// 初始化时不启用 MCP
const agent = new HaiCodeAgent({
  mcp: { enabled: false },
});

// 稍后启用 MCP
await agent.updateMCPConfig({
  enabled: true,
  servers: {
    "time-server": PREDEFINED_MCP_SERVERS["time-server"],
  },
});

// 检查 MCP 状态
const status = agent.getMCPStatus();
console.log(status);
```

### 获取工具信息

```typescript
// 获取所有可用工具
const tools = agent.getToolsInfo();
console.log('Available tools:', tools.map(t => t.name));

// 获取 MCP 状态
const mcpStatus = agent.getMCPStatus();
console.log('MCP Status:', mcpStatus);
```

## 常见 MCP 服务器

### 时间服务器

```bash
# 安装
uvx mcp-server-time

# 配置
{
  "time-server": {
    "command": "uvx",
    "args": ["mcp-server-time"],
    "transport": "stdio"
  }
}
```

### 浏览器工具

```bash
# 安装
uvx mcp-server-browser-tools

# 配置
{
  "browser-tools": {
    "command": "uvx",
    "args": ["mcp-server-browser-tools"],
    "transport": "stdio"
  }
}
```

### 文件系统工具

```bash
# 安装
uvx mcp-server-filesystem

# 配置
{
  "filesystem": {
    "command": "uvx",
    "args": ["mcp-server-filesystem"],
    "transport": "stdio"
  }
}
```

## 创建自定义 MCP 服务器

### Python 示例

```python
# math_server.py
from mcp.server.fastmcp import FastMCP

mcp = FastMCP("Math")

@mcp.tool()
def add(a: int, b: int) -> int:
    """Add two numbers"""
    return a + b

@mcp.tool()
def multiply(a: int, b: int) -> int:
    """Multiply two numbers"""
    return a * b

if __name__ == "__main__":
    mcp.run(transport="stdio")
```

### 配置自定义服务器

```typescript
{
  "custom-math": {
    "command": "python",
    "args": ["/path/to/math_server.py"],
    "transport": "stdio",
    "cwd": "/path/to/server/directory",
  }
}
```

## 错误处理

### 优雅降级

```typescript
const agent = new HaiCodeAgent({
  mcp: {
    enabled: true,
    servers: {
      "unreliable-server": {
        command: "some-command",
        args: ["that-might-fail"],
        transport: "stdio",
      },
    },
    settings: {
      throwOnLoadError: false,  // 不因 MCP 错误而失败
    },
  },
});

// 检查 MCP 是否成功初始化
const status = agent.getMCPStatus();
if (status?.initialized) {
  console.log(`MCP initialized with ${status.toolCount} tools`);
} else {
  console.log('MCP failed to initialize, using core tools only');
}
```

### 严格模式

```typescript
const agent = new HaiCodeAgent({
  mcp: {
    enabled: true,
    servers: { /* 配置 */ },
    settings: {
      throwOnLoadError: true,  // MCP 错误时抛出异常
    },
  },
});
```

## 最佳实践

1. **使用预定义配置**：优先使用 `PREDEFINED_MCP_SERVERS` 中的配置
2. **错误处理**：在生产环境中设置 `throwOnLoadError: false`
3. **超时设置**：根据工具复杂度调整 `defaultToolTimeout`
4. **资源清理**：始终调用 `agent.close()` 清理 MCP 连接
5. **工具命名**：使用 `prefixToolNameWithServerName` 避免工具名冲突

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查命令和参数是否正确
   - 确认依赖已安装
   - 查看错误日志

2. **工具加载失败**
   - 验证服务器配置
   - 检查网络连接（HTTP/SSE）
   - 确认权限设置

3. **性能问题**
   - 调整超时设置
   - 减少并发工具调用
   - 优化服务器响应时间

### 调试技巧

```typescript
// 启用详细日志
const agent = new HaiCodeAgent({
  mcp: {
    enabled: true,
    servers: { /* 配置 */ },
    settings: {
      throwOnLoadError: true,  // 快速失败以便调试
    },
  },
});

// 检查状态
console.log('MCP Status:', agent.getMCPStatus());
console.log('Available Tools:', agent.getToolsInfo());
```

## 参考资源

- [MCP 官方文档](https://modelcontextprotocol.io/)
- [LangChain MCP 适配器](https://github.com/langchain-ai/langchainjs/tree/main/libs/langchain-mcp-adapters)
- [MCP 服务器列表](https://github.com/modelcontextprotocol/servers)
