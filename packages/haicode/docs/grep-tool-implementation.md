# Grep Tool Implementation

## 概述

本文档描述了 `packages/haicode/src/tools/grep.ts` 的实现，该实现基于 `packages/core/src/tools/grep.ts` 的核心逻辑，但遵循 langchainjs 的 tool 定义规范。

## 核心特性

### 1. 与 packages/core 的逻辑一致性

- **多策略搜索**: 实现了相同的三层搜索策略：
  1. Git grep（优先，适用于git仓库）
  2. 系统grep（次选，适用于Unix/Linux系统）
  3. JavaScript实现（兜底，纯JS实现）

- **参数验证**: 包含了相同的参数验证逻辑，确保正则表达式有效性和路径安全性

- **路径安全**: 实现了路径验证，防止访问根目录外的文件

- **错误处理**: 遵循相同的错误处理模式，包括优雅降级

### 2. LangChain.js 兼容性

- **Tool 定义**: 使用 `tool()` 函数创建，符合 langchainjs 规范
- **Schema 验证**: 使用 Zod 进行参数 schema 定义
- **类型安全**: 完全类型化，避免使用 `any` 和 `unknown`
- **错误处理**: 遵循 langchainjs 的错误处理模式

## 主要组件

### 接口定义

```typescript
export interface GrepToolParams {
  pattern: string;
  path?: string | null;
  include?: string | null;
}

interface GrepMatch {
  filePath: string;
  lineNumber: number;
  line: string;
}
```

### 核心函数

1. **resolveAndValidatePath**: 路径解析和安全验证
2. **validateToolParams**: 参数验证
3. **isCommandAvailable**: 检查系统命令可用性
4. **parseGrepOutput**: 解析grep命令输出
5. **isGitRepository**: 检查是否为git仓库
6. **performGrepSearch**: 执行多策略搜索

### Tool 实现

```typescript
export const grepTool = tool(
  async (params: GrepToolParams) => {
    // 实现逻辑
  },
  {
    name: "search_file_content",
    description: "...",
    schema: z.object({
      pattern: z.string().describe("..."),
      path: z.string().nullable().optional().describe("..."),
      include: z.string().nullable().optional().describe("..."),
    }),
  }
);
```

## 功能特性

### 1. 搜索策略

#### Git Grep（优先策略）
- 使用 `git grep --untracked -n -E --ignore-case`
- 支持文件过滤（通过 `--` 参数）
- 自动检测git仓库
- 处理退出码：0（成功）、1（无匹配）、其他（错误）

#### 系统 Grep（次选策略）
- 使用 `grep -r -n -H -E`
- 自动排除常见目录：`.git`, `node_modules`, `bower_components`
- 支持文件包含过滤（`--include`）
- 抑制常见无害错误信息

#### JavaScript 实现（兜底策略）
- 使用 `globStream` 进行文件发现
- 支持相同的忽略模式
- 逐行正则匹配
- 处理文件读取错误

### 2. 参数处理

- **pattern**: 必需，正则表达式模式
- **path**: 可选，搜索目录路径（默认当前目录）
- **include**: 可选，文件过滤glob模式

### 3. 输出格式

```
Found X matches for pattern "pattern" in path "path":
---
File: filename
L123: matching line content
---
```

### 4. 错误处理

- 参数验证错误
- 路径安全检查
- 正则表达式语法验证
- 搜索执行错误
- 优雅降级机制

## 使用示例

```typescript
import { grepTool } from './tools/grep.js';

// 基本搜索
const result1 = await grepTool.invoke({ 
  pattern: 'function.*hello' 
});

// 指定目录搜索
const result2 = await grepTool.invoke({ 
  pattern: 'import.*React',
  path: './src'
});

// 文件类型过滤
const result3 = await grepTool.invoke({ 
  pattern: 'export.*class',
  path: './src',
  include: '*.ts'
});
```

## 测试覆盖

实现包含完整的测试套件：

1. **基本功能测试**: 简单模式匹配
2. **文件过滤测试**: 按扩展名过滤
3. **正则表达式测试**: 复杂正则模式
4. **无匹配测试**: 处理无结果情况
5. **参数验证测试**: 无效正则表达式
6. **路径验证测试**: 无效路径处理
7. **默认路径测试**: null路径处理

## 性能考虑

- 优先使用原生工具（git grep, system grep）获得最佳性能
- JavaScript实现作为兜底，确保跨平台兼容性
- 合理的文件大小限制和错误处理
- 流式文件处理避免内存问题

## 安全特性

- 路径遍历攻击防护
- 正则表达式DoS防护（通过验证）
- 文件访问权限尊重
- 敏感目录自动排除
