import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { logger } from './logger.js';

/**
 * 获取当前包的版本号
 * @returns 版本号字符串
 */
export function getVersion(): string {
  try {
    // 获取当前文件的目录
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = dirname(__filename);
    
    // 从当前文件位置向上查找 package.json
    const packageJsonPath = join(__dirname, '..', '..', 'package.json');
    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
    
    return packageJson.version;
  } catch (_error) {
    // 如果无法读取 package.json，返回默认版本
    logger.warn('无法从 package.json 读取版本号，使用默认版本');
    return '0.0.0';
  }
}
