import { parseArgs } from "node:util";
import { DEFAULT_HAI_CODE_MODEL, DEFAULT_OPENAI_BASE_URL } from "../config/models.js";

export interface CliOptions {
  model?: string;
  prompt?: string;
  baseUrl?: string;
  sessionId?: string;
  debug?: boolean;
  help?: boolean;
  version?: boolean;
  interactive?: boolean;
}

export async function parseCliArguments(): Promise<{ options: CliOptions; prompt?: string }> {
  const { values, positionals } = parseArgs({
    args: process.argv.slice(2),
    options: {
      model: {
        type: 'string',
        short: 'm',
        default: process.env.HAI_CODE_MODEL || DEFAULT_HAI_CODE_MODEL,
      },
      prompt: {
        type: 'string',
        short: 'p',
      },
      'base-url': {
        type: 'string',
        short: 'b',
      },
      interactive: {
        type: 'boolean',
        short: 'i',
        default: false,
      },
      'session-id': {
        type: 'string',
        short: 's',
      },
      debug: {
        type: 'boolean',
        short: 'd',
        default: false,
      },
      help: {
        type: 'boolean',
        short: 'h',
        default: false,
      },
      version: {
        type: 'boolean',
        short: 'v',
        default: false,
      },
    },
    allowPositionals: true,
  });

  const options: CliOptions = {
    model: values.model || DEFAULT_HAI_CODE_MODEL,
    prompt: values.prompt,
    debug: values.debug,
    help: values.help,
    version: values.version,
    baseUrl: values['base-url'] || DEFAULT_OPENAI_BASE_URL,
    sessionId: values['session-id'],
    interactive: values.interactive,
  };

  // If no prompt provided via --prompt, use positional arguments
  const prompt = options.prompt || positionals.join(' ');

  return { options, prompt };
}
