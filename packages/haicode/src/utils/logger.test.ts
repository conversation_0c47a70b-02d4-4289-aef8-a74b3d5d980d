/**
 * 日志系统测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { LogLevel, logger, enableDebugMode, disableDebugMode, isDebugMode, createLogger } from './logger.js';

// Mock console methods
const mockConsole = {
  log: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
};

describe('Logger', () => {
  beforeEach(() => {
    // Mock console methods
    vi.spyOn(console, 'log').mockImplementation(mockConsole.log);
    vi.spyOn(console, 'error').mockImplementation(mockConsole.error);
    vi.spyOn(console, 'warn').mockImplementation(mockConsole.warn);
    vi.spyOn(console, 'info').mockImplementation(mockConsole.info);
    vi.spyOn(console, 'debug').mockImplementation(mockConsole.debug);
    
    // Clear mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('LogLevel', () => {
    it('should have correct level values', () => {
      expect(LogLevel.ERROR).toBe(0);
      expect(LogLevel.WARN).toBe(1);
      expect(LogLevel.INFO).toBe(2);
      expect(LogLevel.DEBUG).toBe(3);
    });
  });

  describe('Logger instance', () => {
    it('should create singleton instance', () => {
      const instance1 = createLogger();
      const instance2 = createLogger();
      expect(instance1).toBe(instance2);
    });

    it('should create instance with custom config', () => {
      const customLogger = createLogger({
        level: LogLevel.DEBUG,
        enableDebug: true,
        prefix: '[Test]',
        enableColors: false,
      });

      expect(customLogger.getConfig().level).toBe(LogLevel.DEBUG);
      expect(customLogger.getConfig().enableDebug).toBe(true);
      expect(customLogger.getConfig().prefix).toBe('[Test]');
      expect(customLogger.getConfig().enableColors).toBe(false);
    });
  });

  describe('Log levels', () => {
    it('should respect log level filtering', () => {
      const testLogger = createLogger({
        level: LogLevel.WARN,
        enableDebug: false,
        enableColors: false,
      });

      testLogger.error('error message');
      testLogger.warn('warn message');
      testLogger.info('info message');
      testLogger.debug('debug message');

      expect(mockConsole.log).toHaveBeenCalledTimes(2); // ERROR and WARN only
      expect(mockConsole.log).toHaveBeenCalledWith(expect.stringContaining('error message'));
      expect(mockConsole.log).toHaveBeenCalledWith(expect.stringContaining('warn message'));
      expect(mockConsole.log).not.toHaveBeenCalledWith(expect.stringContaining('info message'));
      expect(mockConsole.log).not.toHaveBeenCalledWith(expect.stringContaining('debug message'));
    });

    it('should output all levels when set to DEBUG', () => {
      const testLogger = createLogger({
        level: LogLevel.DEBUG,
        enableDebug: true,
        enableColors: false,
      });

      testLogger.error('error message');
      testLogger.warn('warn message');
      testLogger.info('info message');
      testLogger.debug('debug message');

      expect(mockConsole.log).toHaveBeenCalledTimes(4);
      expect(mockConsole.log).toHaveBeenCalledWith(expect.stringContaining('error message'));
      expect(mockConsole.log).toHaveBeenCalledWith(expect.stringContaining('warn message'));
      expect(mockConsole.log).toHaveBeenCalledWith(expect.stringContaining('info message'));
      expect(mockConsole.log).toHaveBeenCalledWith(expect.stringContaining('debug message'));
    });
  });

  describe('Debug mode', () => {
    it('should only output debug logs when debug mode is enabled', () => {
      const testLogger = createLogger({
        level: LogLevel.DEBUG,
        enableDebug: false,
        enableColors: false,
      });

      testLogger.debug('debug message 1');
      testLogger.debugAlways('debug message 2');

      // debug() should not output when debug mode is disabled
      expect(mockConsole.log).toHaveBeenCalledTimes(1);
      expect(mockConsole.log).toHaveBeenCalledWith(expect.stringContaining('debug message 2'));
      expect(mockConsole.log).not.toHaveBeenCalledWith(expect.stringContaining('debug message 1'));
    });

    it('should output debug logs when debug mode is enabled', () => {
      const testLogger = createLogger({
        level: LogLevel.DEBUG,
        enableDebug: true,
        enableColors: false,
      });

      testLogger.debug('debug message 1');
      testLogger.debugAlways('debug message 2');

      expect(mockConsole.log).toHaveBeenCalledTimes(2);
      expect(mockConsole.log).toHaveBeenCalledWith(expect.stringContaining('debug message 1'));
      expect(mockConsole.log).toHaveBeenCalledWith(expect.stringContaining('debug message 2'));
    });
  });

  describe('Context support', () => {
    it('should include context in log messages', () => {
      const testLogger = createLogger({
        enableColors: false,
      });

      testLogger.info('test message', { key: 'value', number: 123 });

      expect(mockConsole.log).toHaveBeenCalledWith(
        expect.stringContaining('test message')
      );
      expect(mockConsole.log).toHaveBeenCalledWith(expect.stringContaining('"key": "value"'));
      expect(mockConsole.log).toHaveBeenCalledWith(expect.stringContaining('"number": 123'));
    });
  });

  describe('Child logger', () => {
    it('should create child logger with prefixed name', () => {
      // 重置全局日志器配置
      logger.updateConfig({ prefix: '[HaiCode]' });
      const childLogger = logger.child('Child');

      expect(childLogger.getConfig().prefix).toBe('[HaiCode] [Child]');
    });
  });

  describe('Global logger functions', () => {
    it('should enable debug mode globally', () => {
      disableDebugMode(); // Reset to known state
      expect(isDebugMode()).toBe(false);

      enableDebugMode();
      expect(isDebugMode()).toBe(true);
    });

    it('should disable debug mode globally', () => {
      enableDebugMode(); // Set to known state
      expect(isDebugMode()).toBe(true);

      disableDebugMode();
      expect(isDebugMode()).toBe(false);
    });

    it('should use global logger instance', () => {
      logger.info('test message');
      expect(mockConsole.log).toHaveBeenCalledWith(expect.stringContaining('test message'));
    });
  });

  describe('Message formatting', () => {
    it('should include timestamp and prefix', () => {
      const testLogger = createLogger({
        prefix: '[Test]',
        enableColors: false,
      });

      testLogger.info('test message');

      const logCall = mockConsole.log.mock.calls[0][0];
      expect(logCall).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z \[Test\] \[INFO\] test message$/);
    });

    it('should include context in separate lines', () => {
      const testLogger = createLogger({
        enableColors: false,
      });

      testLogger.info('test message', { key: 'value' });

      const logCall = mockConsole.log.mock.calls[0][0];
      const lines = logCall.split('\n');
      // 由于 JSON 格式化，可能有多个换行符
      expect(lines.length).toBeGreaterThanOrEqual(2);
      expect(lines[0]).toMatch(/test message$/);
      expect(logCall).toContain('"key": "value"');
    });
  });
});
