import { describe, it, expect } from 'vitest';
import { getVersion } from './version.js';

describe('getVersion', () => {
  it('should return a valid version string', () => {
    const version = getVersion();
    
    // 版本号应该是一个非空字符串
    expect(version).toBeTypeOf('string');
    expect(version.length).toBeGreaterThan(0);
    
    // 版本号应该符合语义化版本格式 (x.y.z 或 x.y.z-alpha.n)
    expect(version).toMatch(/^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$/);
  });
});
