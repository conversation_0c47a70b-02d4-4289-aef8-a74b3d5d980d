/**
 * 日志工具模块
 * 提供统一的日志记录功能，支持不同日志级别和调试模式控制
 */

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

export interface LoggerConfig {
  level: LogLevel;
  enableDebug: boolean;
  prefix?: string;
  enableColors?: boolean;
}

export interface LogContext {
  [key: string]: unknown;
}

class Logger {
  private config: LoggerConfig;
  private static instance: Logger;

  private constructor(config: Partial<LoggerConfig> = {}) {
    // 使用硬编码默认值，配置会在首次使用时动态加载
    const defaultConfig: LoggerConfig = {
      level: LogLevel.INFO,
      enableDebug: false,
      prefix: '[HaiCode]',
      enableColors: true,
    };

    this.config = {
      ...defaultConfig,
      ...config,
    };
  }

  /**
   * 获取单例实例
   */
  static getInstance(config?: Partial<LoggerConfig>): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(config);
      // 动态加载配置
      Logger.instance.loadConfig();
    } else if (config) {
      // 如果提供了配置，更新现有实例
      Logger.instance.updateConfig(config);
    }
    return Logger.instance;
  }

  /**
   * 动态加载配置
   */
  private async loadConfig(): Promise<void> {
    try {
      const { getLoggerConfig } = await import('../config/logger.js');
      const config = getLoggerConfig();
      this.updateConfig(config);
    } catch {
      // 如果配置文件不可用，保持默认配置
    }
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 设置日志级别
   */
  setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  /**
   * 启用/禁用调试模式
   */
  setDebugMode(enabled: boolean): void {
    this.config.enableDebug = enabled;
    if (enabled && this.config.level < LogLevel.DEBUG) {
      this.config.level = LogLevel.DEBUG;
    }
  }

  /**
   * 格式化日志消息
   */
  private formatMessage(level: string, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const prefix = this.config.prefix || '[HaiCode]';
    const levelStr = `[${level}]`;
    
    let formattedMessage = `${timestamp} ${prefix} ${levelStr} ${message}`;
    
    if (context && Object.keys(context).length > 0) {
      const contextStr = JSON.stringify(context, null, 2);
      formattedMessage += `\n${contextStr}`;
    }
    
    return formattedMessage;
  }

  /**
   * 带颜色的日志输出
   */
  private logWithColor(level: LogLevel, message: string, context?: LogContext): void {
    if (level > this.config.level) {
      return;
    }

    const formattedMessage = this.formatMessage(
      LogLevel[level],
      message,
      context
    );

    if (this.config.enableColors) {
      const colors = {
        [LogLevel.ERROR]: '\x1b[31m', // 红色
        [LogLevel.WARN]: '\x1b[33m',  // 黄色
        [LogLevel.INFO]: '\x1b[36m',  // 青色
        [LogLevel.DEBUG]: '\x1b[90m', // 灰色
      };
      const reset = '\x1b[0m';
      
      console.log(`${colors[level]}${formattedMessage}${reset}`);
    } else {
      console.log(formattedMessage);
    }
  }

  /**
   * 错误日志
   */
  error(message: string, context?: LogContext): void {
    this.logWithColor(LogLevel.ERROR, message, context);
  }

  /**
   * 警告日志
   */
  warn(message: string, context?: LogContext): void {
    this.logWithColor(LogLevel.WARN, message, context);
  }

  /**
   * 信息日志
   */
  info(message: string, context?: LogContext): void {
    this.logWithColor(LogLevel.INFO, message, context);
  }

  /**
   * 调试日志 - 仅在调试模式启用时输出
   */
  debug(message: string, context?: LogContext): void {
    if (this.config.enableDebug) {
      this.logWithColor(LogLevel.DEBUG, message, context);
    }
  }

  /**
   * 调试日志 - 始终输出（如果日志级别允许）
   */
  debugAlways(message: string, context?: LogContext): void {
    this.logWithColor(LogLevel.DEBUG, message, context);
  }

  /**
   * 创建子日志器
   */
  child(prefix: string): Logger {
    const childLogger = new Logger({
      ...this.config,
      prefix: `${this.config.prefix} [${prefix}]`,
    });
    // 不调用 loadConfig，避免覆盖配置
    return childLogger;
  }

  /**
   * 获取当前配置
   */
  getConfig(): LoggerConfig {
    return { ...this.config };
  }
}

// 导出便捷函数
export const logger = Logger.getInstance();

/**
 * 创建日志器实例
 */
export function createLogger(config?: Partial<LoggerConfig>): Logger {
  return Logger.getInstance(config);
}

/**
 * 设置全局日志级别
 */
export function setLogLevel(level: LogLevel): void {
  logger.setLevel(level);
}

/**
 * 启用调试模式
 */
export function enableDebugMode(): void {
  logger.setDebugMode(true);
}

/**
 * 禁用调试模式
 */
export function disableDebugMode(): void {
  logger.setDebugMode(false);
}

/**
 * 检查是否启用调试模式
 */
export function isDebugMode(): boolean {
  return logger.getConfig().enableDebug;
}

/**
 * 便捷的日志函数
 */
export const log = {
  error: (message: string, context?: LogContext) => logger.error(message, context),
  warn: (message: string, context?: LogContext) => logger.warn(message, context),
  info: (message: string, context?: LogContext) => logger.info(message, context),
  debug: (message: string, context?: LogContext) => logger.debug(message, context),
  debugAlways: (message: string, context?: LogContext) => logger.debugAlways(message, context),
};
