/**
 * CLI 功能测试
 */

import { describe, it, expect } from 'vitest';
import { parseCliArguments } from './utils/parseArgs.js';
import { HaiCodeAgent } from './index.js';

describe('CLI 参数解析', () => {
  it('应该正确解析基本参数', async () => {
    // 模拟命令行参数
    const originalArgv = process.argv;
    process.argv = ['node', 'cli.js', '-m', 'test-model', '-p', 'test prompt'];

    const { options, prompt } = await parseCliArguments();

    expect(options.model).toBe('test-model');
    expect(options.prompt).toBe('test prompt');
    expect(prompt).toBe('test prompt');

    process.argv = originalArgv;
  });

  it('应该正确解析位置参数', async () => {
    const originalArgv = process.argv;
    process.argv = ['node', 'cli.js', 'hello', 'world'];

    const { prompt } = await parseCliArguments();

    expect(prompt).toBe('hello world');

    process.argv = originalArgv;
  });

  it('应该使用默认值', async () => {
    const originalArgv = process.argv;
    process.argv = ['node', 'cli.js'];

    const { options } = await parseCliArguments();

    expect(options.model).toBe('ht::saas-deepseek-v3');
    expect(options.debug).toBe(false);
    expect(options.interactive).toBe(false);

    process.argv = originalArgv;
  });
});

describe('HaiCodeAgent', () => {
  it('应该能够创建 agent 实例', () => {
    const agent = new HaiCodeAgent();
    expect(agent).toBeInstanceOf(HaiCodeAgent);
  });

  it('应该能够使用自定义配置创建 agent', () => {
    const config = {
      model: 'test-model',
      baseUrl: 'http://test.com',
      apiKey: 'test-key',
    };

    const agent = new HaiCodeAgent(config);
    expect(agent).toBeInstanceOf(HaiCodeAgent);
  });

  // 注意：实际的 LLM 调用测试需要真实的 API 密钥，这里只测试基本功能
  it('应该有 streamMessage 方法', () => {
    const agent = new HaiCodeAgent();
    expect(typeof agent.streamMessage).toBe('function');
  });

  it('应该有 processMessage 方法', () => {
    const agent = new HaiCodeAgent();
    expect(typeof agent.processMessage).toBe('function');
  });
});

describe('环境变量处理', () => {
  it('应该从环境变量读取模型配置', async () => {
    const originalEnv = process.env.HAI_CODE_MODEL;
    process.env.HAI_CODE_MODEL = 'env-test-model';

    const originalArgv = process.argv;
    process.argv = ['node', 'cli.js'];

    const { options } = await parseCliArguments();

    expect(options.model).toBe('env-test-model');

    process.argv = originalArgv;
    if (originalEnv !== undefined) {
      process.env.HAI_CODE_MODEL = originalEnv;
    } else {
      delete process.env.HAI_CODE_MODEL;
    }
  });

  it('应该从环境变量读取 base URL', async () => {
    const originalEnv = process.env.OPENAI_BASE_URL;
    process.env.OPENAI_BASE_URL = 'http://test-env.com';

    const originalArgv = process.argv;
    process.argv = ['node', 'cli.js'];

    const { options } = await parseCliArguments();

    expect(options.baseUrl).toBe('http://test-env.com');

    process.argv = originalArgv;
    if (originalEnv !== undefined) {
      process.env.OPENAI_BASE_URL = originalEnv;
    } else {
      delete process.env.OPENAI_BASE_URL;
    }
  });
});
