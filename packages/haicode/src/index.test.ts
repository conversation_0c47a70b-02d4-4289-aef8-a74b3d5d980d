/**
 * Tests for HaiCodeAgent MCP integration
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { HaiCodeAgent } from './index.js';

// Mock the MCP client
vi.mock('./mcp/client.js', () => ({
  createMCPClient: vi.fn().mockImplementation(() => ({
    getTools: vi.fn().mockResolvedValue([
      {
        name: 'mcp_test_tool',
        description: 'A test MCP tool',
        schema: {},
      },
    ]),
    getStatus: vi.fn().mockReturnValue({
      enabled: true,
      initialized: true,
      serverCount: 1,
      toolCount: 1,
      servers: ['test-server'],
    }),
    close: vi.fn().mockResolvedValue(undefined),
  })),
}));

// Mock the core tools
vi.mock('./tools/index.js', () => ({
  coreTools: [
    {
      name: 'core_test_tool',
      description: 'A test core tool',
      schema: {},
    },
  ],
}));

// Mock Lang<PERSON>hain components
vi.mock('@langchain/langgraph', () => ({
  Annotation: {
    Root: vi.fn().mockReturnValue({
      State: {},
    }),
  },
  StateGraph: vi.fn().mockImplementation(() => ({
    addNode: vi.fn().mockReturnThis(),
    addEdge: vi.fn().mockReturnThis(),
    addConditionalEdges: vi.fn().mockReturnThis(),
    compile: vi.fn().mockReturnValue({
      stream: vi.fn().mockResolvedValue([]),
    }),
  })),
  END: 'END',
}));

vi.mock('@langchain/langgraph/prebuilt', () => ({
  ToolNode: vi.fn(),
}));

vi.mock('@langchain/openai', () => ({
  ChatOpenAI: vi.fn().mockImplementation(() => ({
    bindTools: vi.fn().mockReturnThis(),
  })),
}));

vi.mock('./utils/langfuseHelper.js', () => ({
  getLangfuseHelper: vi.fn().mockReturnValue({
    isEnabled: vi.fn().mockReturnValue(false),
    createCallbackHandler: vi.fn(),
  }),
}));

vi.mock('./prompt.js', () => ({
  getHaiAgentSystemPrompt: vi.fn().mockReturnValue('Test system prompt'),
}));

describe('HaiCodeAgent MCP Integration', () => {
  let agent: HaiCodeAgent;

  afterEach(async () => {
    if (agent) {
      await agent.close();
    }
    vi.clearAllMocks();
  });

  describe('MCP Configuration', () => {
    it('should create agent without MCP', () => {
      agent = new HaiCodeAgent({});
      
      const mcpStatus = agent.getMCPStatus();
      expect(mcpStatus).toBeNull();
    });

    it('should create agent with MCP disabled', () => {
      agent = new HaiCodeAgent({
        mcp: {
          enabled: false,
        },
      });
      
      const mcpStatus = agent.getMCPStatus();
      expect(mcpStatus).toBeNull();
    });

    it('should create agent with MCP enabled', () => {
      agent = new HaiCodeAgent({
        mcp: {
          enabled: true,
          servers: {
            'test-server': {
              command: 'echo',
              args: ['test'],
              transport: 'stdio',
            },
          },
        },
      });
      
      const mcpStatus = agent.getMCPStatus();
      expect(mcpStatus).toBeDefined();
      expect(mcpStatus?.enabled).toBe(true);
    });
  });

  describe('Tool Integration', () => {
    beforeEach(() => {
      agent = new HaiCodeAgent({
        mcp: {
          enabled: true,
          servers: {
            'test-server': {
              command: 'echo',
              args: ['test'],
              transport: 'stdio',
            },
          },
        },
      });
    });

    it('should include both core and MCP tools', async () => {
      // Trigger tool initialization
      await agent.processMessage('test');
      
      const toolsInfo = agent.getToolsInfo();
      expect(toolsInfo).toHaveLength(2); // 1 core + 1 MCP tool
      
      const toolNames = toolsInfo.map(t => t.name);
      expect(toolNames).toContain('core_test_tool');
      expect(toolNames).toContain('mcp_test_tool');
    });

    it('should get MCP status', () => {
      const mcpStatus = agent.getMCPStatus();
      
      expect(mcpStatus).toBeDefined();
      expect(mcpStatus?.enabled).toBe(true);
      expect(mcpStatus?.initialized).toBe(true);
      expect(mcpStatus?.serverCount).toBe(1);
      expect(mcpStatus?.toolCount).toBe(1);
      expect(mcpStatus?.servers).toEqual(['test-server']);
    });
  });

  describe('MCP Configuration Updates', () => {
    beforeEach(() => {
      agent = new HaiCodeAgent({
        mcp: {
          enabled: false,
        },
      });
    });

    it('should update MCP configuration', async () => {
      // Initially disabled
      expect(agent.getMCPStatus()).toBeNull();
      
      // Enable MCP
      await agent.updateMCPConfig({
        enabled: true,
        servers: {
          'new-server': {
            command: 'echo',
            args: ['new'],
            transport: 'stdio',
          },
        },
      });
      
      const mcpStatus = agent.getMCPStatus();
      expect(mcpStatus?.enabled).toBe(true);
      expect(mcpStatus?.servers).toEqual(['new-server']);
    });

    it('should disable MCP', async () => {
      // Enable first
      await agent.updateMCPConfig({
        enabled: true,
        servers: {
          'test-server': {
            command: 'echo',
            args: ['test'],
            transport: 'stdio',
          },
        },
      });
      
      expect(agent.getMCPStatus()?.enabled).toBe(true);
      
      // Then disable
      await agent.updateMCPConfig({
        enabled: false,
      });
      
      expect(agent.getMCPStatus()).toBeNull();
    });
  });

  describe('Error Handling', () => {
    it('should handle MCP initialization errors gracefully', () => {
      // Mock MCP client creation to throw error
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const { createMCPClient } = require('./mcp/client.js');
      createMCPClient.mockImplementationOnce(() => {
        throw new Error('MCP initialization failed');
      });
      
      // Should not throw during construction
      expect(() => {
        agent = new HaiCodeAgent({
          mcp: {
            enabled: true,
            servers: {
              'failing-server': {
                command: 'invalid-command',
                args: [],
                transport: 'stdio',
              },
            },
          },
        });
      }).not.toThrow();
    });

    it('should handle MCP tool loading errors', async () => {
      const { createMCPClient } = require('./mcp/client.js');
      createMCPClient.mockImplementationOnce(() => ({
        getTools: vi.fn().mockRejectedValue(new Error('Tool loading failed')),
        getStatus: vi.fn().mockReturnValue({
          enabled: true,
          initialized: false,
          serverCount: 0,
          toolCount: 0,
          servers: [],
        }),
        close: vi.fn().mockResolvedValue(undefined),
      }));
      
      agent = new HaiCodeAgent({
        mcp: {
          enabled: true,
          servers: {
            'failing-server': {
              command: 'invalid-command',
              args: [],
              transport: 'stdio',
            },
          },
        },
      });
      
      // Should still work with core tools only
      await expect(agent.processMessage('test')).resolves.not.toThrow();
    });
  });

  describe('Resource Cleanup', () => {
    beforeEach(() => {
      agent = new HaiCodeAgent({
        mcp: {
          enabled: true,
          servers: {
            'test-server': {
              command: 'echo',
              args: ['test'],
              transport: 'stdio',
            },
          },
        },
      });
    });

    it('should close MCP connections', async () => {
      const { createMCPClient } = require('./mcp/client.js');
      const mockClose = vi.fn().mockResolvedValue(undefined);
      createMCPClient.mockReturnValue({
        getTools: vi.fn().mockResolvedValue([]),
        getStatus: vi.fn().mockReturnValue({
          enabled: true,
          initialized: true,
          serverCount: 1,
          toolCount: 0,
          servers: ['test-server'],
        }),
        close: mockClose,
      });
      
      await agent.closeMCP();
      expect(mockClose).toHaveBeenCalled();
    });

    it('should close all connections', async () => {
      const { createMCPClient } = require('./mcp/client.js');
      const mockClose = vi.fn().mockResolvedValue(undefined);
      createMCPClient.mockReturnValue({
        getTools: vi.fn().mockResolvedValue([]),
        getStatus: vi.fn().mockReturnValue({
          enabled: true,
          initialized: true,
          serverCount: 1,
          toolCount: 0,
          servers: ['test-server'],
        }),
        close: mockClose,
      });
      
      await agent.close();
      expect(mockClose).toHaveBeenCalled();
    });
  });
});
