/**
 * Tests for MCP Client functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { HaiAgentMCPClient, createMCPClient, testMCPServerConnection } from './client.js';
import type { HaiAgentMCPConfig } from '../config/mcp.js';

// Mock the MultiServerMCPClient
vi.mock('@langchain/mcp-adapters', () => ({
  MultiServerMCPClient: vi.fn().mockImplementation(() => ({
    initializeConnections: vi.fn().mockResolvedValue({}),
    getTools: vi.fn().mockResolvedValue([
      {
        name: 'test_tool',
        description: 'A test tool',
        schema: {},
      },
    ]),
    getClient: vi.fn().mockResolvedValue({}),
    close: vi.fn().mockResolvedValue(undefined),
  })),
}));

describe('HaiAgentMCPClient', () => {
  let client: HaiAgentMCPClient;
  let mockConfig: HaiAgentMCPConfig;

  beforeEach(() => {
    mockConfig = {
      enabled: true,
      servers: {
        'test-server': {
          command: 'echo',
          args: ['test'],
          transport: 'stdio',
        },
      },
      settings: {
        prefixToolNameWithServerName: true,
        throwOnLoadError: false,
        defaultToolTimeout: 30000,
      },
    };
    client = new HaiAgentMCPClient(mockConfig);
  });

  afterEach(async () => {
    await client.close();
    vi.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create client with config', () => {
      expect(client).toBeInstanceOf(HaiAgentMCPClient);
      expect(client.getServerNames()).toEqual(['test-server']);
    });
  });

  describe('initialize', () => {
    it('should initialize when enabled', async () => {
      await client.initialize();
      expect(client.isEnabled()).toBe(true);
      
      const status = client.getStatus();
      expect(status.enabled).toBe(true);
      expect(status.initialized).toBe(true);
      expect(status.serverCount).toBe(1);
      expect(status.servers).toEqual(['test-server']);
    });

    it('should not initialize when disabled', async () => {
      const disabledConfig = { ...mockConfig, enabled: false };
      const disabledClient = new HaiAgentMCPClient(disabledConfig);
      
      await disabledClient.initialize();
      expect(disabledClient.isEnabled()).toBe(false);
      
      await disabledClient.close();
    });

    it('should handle initialization errors gracefully when throwOnLoadError is false', async () => {
      const errorConfig = {
        ...mockConfig,
        settings: { ...mockConfig.settings, throwOnLoadError: false },
      };
      
      // Mock initialization failure
      const { MultiServerMCPClient } = await import('@langchain/mcp-adapters');
      vi.mocked(MultiServerMCPClient).mockImplementationOnce(() => {
        throw new Error('Initialization failed');
      });
      
      const errorClient = new HaiAgentMCPClient(errorConfig);
      
      // Should not throw
      await expect(errorClient.initialize()).resolves.not.toThrow();
      expect(errorClient.isEnabled()).toBe(false);
      
      await errorClient.close();
    });

    it('should throw initialization errors when throwOnLoadError is true', async () => {
      const errorConfig = {
        ...mockConfig,
        settings: { ...mockConfig.settings, throwOnLoadError: true },
      };
      
      // Mock initialization failure
      const { MultiServerMCPClient } = await import('@langchain/mcp-adapters');
      vi.mocked(MultiServerMCPClient).mockImplementationOnce(() => {
        throw new Error('Initialization failed');
      });
      
      const errorClient = new HaiAgentMCPClient(errorConfig);
      
      await expect(errorClient.initialize()).rejects.toThrow('Failed to initialize MCP client');
      
      await errorClient.close();
    });
  });

  describe('getTools', () => {
    it('should return tools after initialization', async () => {
      const tools = await client.getTools();
      expect(tools).toHaveLength(1);
      expect(tools[0].name).toBe('test_tool');
    });

    it('should return empty array when not initialized', async () => {
      const disabledClient = new HaiAgentMCPClient({ enabled: false });
      const tools = await disabledClient.getTools();
      expect(tools).toEqual([]);
      
      await disabledClient.close();
    });
  });

  describe('getToolsFromServers', () => {
    it('should return tools from specific servers', async () => {
      await client.initialize();
      const tools = await client.getToolsFromServers('test-server');
      expect(tools).toHaveLength(1);
    });

    it('should handle server not found gracefully', async () => {
      await client.initialize();
      const tools = await client.getToolsFromServers('non-existent-server');
      expect(tools).toEqual([]);
    });
  });

  describe('getServerClient', () => {
    it('should return server client', async () => {
      await client.initialize();
      const serverClient = await client.getServerClient('test-server');
      expect(serverClient).toBeDefined();
    });

    it('should return undefined for non-existent server', async () => {
      await client.initialize();
      const serverClient = await client.getServerClient('non-existent');
      expect(serverClient).toBeUndefined();
    });
  });

  describe('updateConfig', () => {
    it('should update configuration and reinitialize', async () => {
      await client.initialize();
      expect(client.getServerNames()).toEqual(['test-server']);
      
      const newConfig: HaiAgentMCPConfig = {
        enabled: true,
        servers: {
          'new-server': {
            command: 'echo',
            args: ['new'],
            transport: 'stdio',
          },
        },
      };
      
      await client.updateConfig(newConfig);
      expect(client.getServerNames()).toEqual(['new-server']);
    });

    it('should disable MCP when config is disabled', async () => {
      await client.initialize();
      expect(client.isEnabled()).toBe(true);
      
      await client.updateConfig({ enabled: false });
      expect(client.isEnabled()).toBe(false);
    });
  });

  describe('close', () => {
    it('should close connections properly', async () => {
      await client.initialize();
      expect(client.isEnabled()).toBe(true);
      
      await client.close();
      expect(client.isEnabled()).toBe(false);
    });
  });
});

describe('createMCPClient', () => {
  it('should create MCP client instance', () => {
    const config: HaiAgentMCPConfig = {
      enabled: true,
      servers: {},
    };
    
    const client = createMCPClient(config);
    expect(client).toBeInstanceOf(HaiAgentMCPClient);
  });
});

describe('testMCPServerConnection', () => {
  it('should test server connection successfully', async () => {
    const result = await testMCPServerConnection('test-server', {
      command: 'echo',
      args: ['test'],
      transport: 'stdio',
    });
    
    expect(result.success).toBe(true);
    expect(result.toolCount).toBe(1);
    expect(result.error).toBeUndefined();
  });

  it('should handle connection failure', async () => {
    // Mock failure
    const { MultiServerMCPClient } = await import('@langchain/mcp-adapters');
    vi.mocked(MultiServerMCPClient).mockImplementationOnce(() => {
      throw new Error('Connection failed');
    });
    
    const result = await testMCPServerConnection('test-server', {
      command: 'invalid-command',
      args: [],
      transport: 'stdio',
    });
    
    expect(result.success).toBe(false);
    expect(result.error).toContain('Connection failed');
    expect(result.toolCount).toBeUndefined();
  });
});
