/**
 * MCP (Model Context Protocol) Integration for HaiCodeAgent
 * 
 * This module provides MCP integration capabilities for HaiCodeAgent,
 * allowing the agent to use tools from external MCP servers.
 */

// Export client functionality
export {
  HaiAgentMCPClient,
  createMCPClient,
  testMCPServerConnection,
} from './client.js';

// Export configuration types and utilities
export type {
  MCPServerBaseConfig,
  MCPServerStdioConfig,
  MCPServerHttpConfig,
  MCPServerConfig,
  MCPMultiServerConfig,
  HaiAgentMCPConfig,
  OAuthClientProvider,
} from '../config/mcp.js';

export {
  createMCPServerConfig,
  validateMCPServerConfig,
  PREDEFINED_MCP_SERVERS,
  DEFAULT_MCP_CONFIG,
} from '../config/mcp.js';
