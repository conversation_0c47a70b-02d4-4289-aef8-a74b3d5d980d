/**
 * MCP Client Manager for HaiCodeAgent
 * 
 * This module provides a wrapper around the LangChain MCP adapters
 * to manage MCP server connections and tool integration.
 */

import { MultiServerMCPClient } from "@langchain/mcp-adapters";
import type { DynamicStructuredTool } from "@langchain/core/tools";
import type {
  HaiAgentMCPConfig,
  MCPServerConfig
} from "../config/mcp.js";
import { logger } from "../utils/logger.js";

// 创建 MCP 客户端专用的日志器
const mcpLogger = logger.child('MCPClient');

/**
 * MCP Client Manager for HaiCodeAgent
 * 
 * Manages connections to multiple MCP servers and provides
 * LangChain-compatible tools for use in the agent.
 */
export class HaiAgentMCPClient {
  private client: MultiServerMCPClient | null = null;
  private config: HaiAgentMCPConfig;
  private isInitialized = false;
  private tools: DynamicStructuredTool[] = [];

  constructor(config: HaiAgentMCPConfig) {
    this.config = config;
  }

  /**
   * Initialize the MCP client and establish connections to all configured servers
   */
  async initialize(): Promise<void> {
    if (this.isInitialized || !this.config.enabled || !this.config.servers) {
      return;
    }

    try {
      // Create and initialize the MCP client
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      this.client = new MultiServerMCPClient({
        mcpServers: this.config.servers || {},
        prefixToolNameWithServerName: this.config.settings?.prefixToolNameWithServerName ?? true,
        additionalToolNamePrefix: this.config.settings?.additionalToolNamePrefix,
        throwOnLoadError: this.config.settings?.throwOnLoadError ?? false,
        defaultToolTimeout: this.config.settings?.defaultToolTimeout ?? 30000,
      } as any);
      
      // Initialize connections and get tools
      await this.client.initializeConnections();
      this.tools = await this.client.getTools();
      
      this.isInitialized = true;
      
      mcpLogger.info('MCP 客户端初始化成功', { 
        serverCount: Object.keys(this.config.servers).length, 
        toolCount: this.tools.length 
      });
    } catch (error) {
      const shouldThrow = this.config.settings?.throwOnLoadError ?? false;
      const errorMessage = `Failed to initialize MCP client: ${error instanceof Error ? error.message : String(error)}`;
      
      if (shouldThrow) {
        throw new Error(errorMessage);
      } else {
        mcpLogger.warn('MCP 客户端初始化失败', { error: error instanceof Error ? error.message : String(error) });
        // Continue without MCP tools
        this.tools = [];
      }
    }
  }

  /**
   * Get all available MCP tools
   */
  async getTools(): Promise<DynamicStructuredTool[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    return this.tools;
  }

  /**
   * Get tools from specific servers
   */
  async getToolsFromServers(...serverNames: string[]): Promise<DynamicStructuredTool[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    if (!this.client) {
      return [];
    }

    try {
      return await this.client.getTools(...serverNames);
    } catch (error) {
      mcpLogger.warn('从服务器获取工具失败', { 
        servers: serverNames, 
        error: error instanceof Error ? error.message : String(error) 
      });
      return [];
    }
  }

  /**
   * Get the underlying MCP client for a specific server
   */
  async getServerClient(serverName: string) {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    if (!this.client) {
      return undefined;
    }

    try {
      return await this.client.getClient(serverName);
    } catch (error) {
      mcpLogger.warn('获取服务器客户端失败', { 
        serverName, 
        error: error instanceof Error ? error.message : String(error) 
      });
      return undefined;
    }
  }

  /**
   * Check if MCP is enabled and initialized
   */
  isEnabled(): boolean {
    return Boolean(this.config.enabled && this.isInitialized);
  }

  /**
   * Get the list of configured server names
   */
  getServerNames(): string[] {
    return Object.keys(this.config.servers || {});
  }

  /**
   * Get MCP status information
   */
  getStatus(): {
    enabled: boolean;
    initialized: boolean;
    serverCount: number;
    toolCount: number;
    servers: string[];
  } {
    return {
      enabled: Boolean(this.config.enabled),
      initialized: this.isInitialized,
      serverCount: Object.keys(this.config.servers || {}).length,
      toolCount: this.tools.length,
      servers: this.getServerNames(),
    };
  }

  /**
   * Close all MCP connections
   */
  async close(): Promise<void> {
    if (this.client) {
      try {
        await this.client.close();
      } catch (error) {
        mcpLogger.warn('关闭 MCP 客户端时出错', { error: error instanceof Error ? error.message : String(error) });
      } finally {
        this.client = null;
        this.isInitialized = false;
        this.tools = [];
      }
    }
  }

  /**
   * Update MCP configuration and reinitialize if needed
   */
  async updateConfig(newConfig: HaiAgentMCPConfig): Promise<void> {
    // Close existing connections
    await this.close();
    
    // Update configuration
    this.config = newConfig;
    
    // Reinitialize if enabled
    if (newConfig.enabled) {
      await this.initialize();
    }
  }


}

/**
 * Create a new MCP client instance
 */
export function createMCPClient(config: HaiAgentMCPConfig): HaiAgentMCPClient {
  return new HaiAgentMCPClient(config);
}

/**
 * Utility function to test MCP server connectivity
 */
export async function testMCPServerConnection(
  serverName: string, 
  serverConfig: MCPServerConfig
): Promise<{ success: boolean; error?: string; toolCount?: number }> {
  const testConfig: HaiAgentMCPConfig = {
    enabled: true,
    servers: { [serverName]: serverConfig },
    settings: { throwOnLoadError: true },
  };

  const client = createMCPClient(testConfig);
  
  try {
    await client.initialize();
    const tools = await client.getTools();
    await client.close();
    
    return {
      success: true,
      toolCount: tools.length,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}
