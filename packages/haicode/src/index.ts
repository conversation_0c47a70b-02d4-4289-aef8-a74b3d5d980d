import { StateGraph, END } from "@langchain/langgraph";
import { Annotation } from "@langchain/langgraph";
import { AIMessage, SystemMessage, AIMessageChunk, HumanMessage, BaseMessage } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { MemorySaver } from "@langchain/langgraph";
import type { BaseCheckpointSaver } from "@langchain/langgraph";
import { coreTools } from "./tools/index.js";
import { isAIMessageChunk } from "@langchain/core/messages";
import { DEFAULT_OPENAI_CONFIGS } from "./config/models.js";
import { getLangfuseHelper } from "./utils/langfuseHelper.js";
import type { CallbackHandler } from "langfuse-langchain";
import { getHaiAgentSystemPrompt } from "./prompt.js";
import type { HaiAgentMCPConfig } from "./config/mcp.js";
import { createMCPClient, type HaiAgentMCPClient } from "./mcp/client.js";
import type { DynamicStructuredTool } from "@langchain/core/tools";
import { getUserInfo } from "./utils/user.js";
import { getUUID } from "./utils/uuid.js";
import { getVersion } from "./utils/version.js";
import { logger } from "./utils/logger.js";

// 1. Define the state
const StateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
  }),
});

type AgentStateType = typeof StateAnnotation.State;

export interface AgentConfig {
  model?: string;
  baseUrl?: string;
  apiKey?: string;
  temperature?: number;
  maxTokens?: number;
  // 系统提示词配置
  systemPrompt?: string;
  userMemory?: string;
  // Langfuse 配置
  langfuse?: {
    enabled?: boolean;
    userId?: string;
    sessionId?: string;
    release?: string;
    version?: string;
  };
  // MCP 配置
  mcp?: HaiAgentMCPConfig;
  // 会话持久化配置
  enablePersistence?: boolean;
  maxIterations?: number;
}

type ToolCallChunk = NonNullable<AIMessageChunk['tool_call_chunks']>[0];

export class HaiCodeAgent {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private compiledWorkflow: any;
  private config: AgentConfig;
  private langfuseHandler: CallbackHandler | null = null;
  private systemPromptCache: string | null = null;
  private mcpClient: HaiAgentMCPClient | null = null;
  private allTools: DynamicStructuredTool[] = [];
  private checkpointer?: BaseCheckpointSaver;
  private maxIterations: number = 25;

  constructor(config: AgentConfig = {}) {
    this.config = config;
    this.maxIterations = config.maxIterations || 25;
    this.initializeLangfuse();
    this.initializeMCP();
    this.initializeCheckpointer();
    // 延迟初始化工作流，因为需要异步加载 MCP 工具
    this.compiledWorkflow = null;
  }

  private initializeLangfuse(): void {
    const langfuseHelper = getLangfuseHelper();

    // 检查是否启用 Langfuse
    if (this.config.langfuse?.enabled !== false && langfuseHelper.isEnabled()) {
      this.langfuseHandler = langfuseHelper.createCallbackHandler({
        userId: this.config.langfuse?.userId || getUserInfo().userName,
        sessionId: this.config.langfuse?.sessionId || getUUID(),
        release: this.config.langfuse?.release,
        version: this.config.langfuse?.version || getVersion(),
      });
    }
  }

  private initializeMCP(): void {
    // 初始化 MCP 客户端
    if (this.config.mcp?.enabled) {
      this.mcpClient = createMCPClient(this.config.mcp);
    }
  }

  private initializeCheckpointer(): void {
    // 初始化 checkpointer 用于会话持久化
    if (this.config.enablePersistence !== false) {
      this.checkpointer = new MemorySaver();
      logger.debug('[HaiCodeAgent] MemorySaver checkpointer initialized for session persistence');
    }
  }

  private async initializeTools(): Promise<void> {
    // 开始使用核心工具
    this.allTools = [...coreTools];

    // 如果启用了 MCP，添加 MCP 工具
    if (this.mcpClient) {
      try {
        const mcpTools = await this.mcpClient.getTools();
        this.allTools.push(...mcpTools);
        logger.info(`添加了 ${mcpTools.length} 个 MCP 工具到代理`, { toolCount: mcpTools.length });
      } catch (error) {
        logger.warn('加载 MCP 工具失败', { error: error instanceof Error ? error.message : String(error) });
      }
    }
  }

  private async initializeWorkflow() {
    // 2. Set up the tools - combine core tools with MCP tools
    await this.initializeTools();
    const toolNode = new ToolNode(this.allTools);

    // 3. Set up the model with custom config
    const modelConfig = {
      ...DEFAULT_OPENAI_CONFIGS,
      modelName: this.config.model || DEFAULT_OPENAI_CONFIGS.modelName,
      apiKey: this.config.apiKey || DEFAULT_OPENAI_CONFIGS.apiKey,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      configuration: {
        ...DEFAULT_OPENAI_CONFIGS.configuration,
        baseURL: this.config.baseUrl || DEFAULT_OPENAI_CONFIGS.configuration.baseURL,
      },
    };

    const model = new ChatOpenAI(modelConfig);
    const boundModel = model.bindTools(this.allTools);

    // 4. Define the graph
    const routeMessage = (state: AgentStateType) => {
      const { messages } = state;
      const lastMessage = messages[messages.length - 1] as AIMessage;
      // If no tools are called, we can finish (respond to the user)
      if (!lastMessage?.tool_calls?.length) {
        return END;
      }
      // Otherwise if there is, we continue and call the tools
      return "tools";
    };

    const callModel = async (
      state: AgentStateType,
    ) => {
      // Following LangGraph.js best practices for system prompt handling
      // Based on the official documentation pattern
      const { messages } = state;

      // Get system prompt dynamically - this follows the recommended pattern
      // of building messages array with system prompt at the beginning
      const systemPrompt = this.getCurrentSystemPrompt();

      // Build messages array with system prompt at the beginning
      // This is the core LangGraph.js recommended pattern from the documentation
      const messagesWithSystem = [
        new SystemMessage(systemPrompt),
        ...messages
      ];

      const responseMessage = await boundModel.invoke(messagesWithSystem);
      return { messages: [responseMessage] };
    };

    const workflow = new StateGraph(StateAnnotation)
      .addNode("agent", callModel)
      .addNode("tools", toolNode)
      .addEdge("__start__", "agent")
      .addConditionalEdges("agent", routeMessage)
      .addEdge("tools", "agent");

    // 编译时添加 checkpointer 以支持会话持久化
    const compileOptions: { checkpointer?: BaseCheckpointSaver } = {};
    if (this.checkpointer) {
      compileOptions.checkpointer = this.checkpointer;
    }

    return workflow.compile(compileOptions);
  }

  private async ensureWorkflowInitialized(): Promise<void> {
    if (!this.compiledWorkflow) {
      this.compiledWorkflow = await this.initializeWorkflow();
    }
  }

  async *streamMessage(message: string, sessionId?: string): AsyncGenerator<string, void, unknown> {
    // 确保工作流已初始化
    await this.ensureWorkflowInitialized();

    // 生成或使用提供的 sessionId
    const threadId = sessionId || getUUID();

    // 创建初始状态 - LangGraph MemorySaver 会自动合并现有线程状态
    const initialState = {
      messages: [new HumanMessage(message)],
      userMemory: this.config.userMemory || "",
      iterationCount: 0,
    };

    // 准备配置，包含 Langfuse 回调和 thread_id
    const config: Record<string, unknown> = {
      streamMode: "messages",
      recursionLimit: this.maxIterations,
      configurable: {
        thread_id: threadId, // 这是 MemorySaver 所需的
      },
    };

    if (this.langfuseHandler) {
      config.callbacks = [this.langfuseHandler];
    }

    const stream = await this.compiledWorkflow.stream(
      initialState,
      config,
    );

    for await (const [messageChunk, _metadata] of stream) {
      if (isAIMessageChunk(messageChunk) && messageChunk.tool_call_chunks?.length) {
        // For tool calls, we might want to show some indication but not the raw args
        yield* this.handleToolCallChunks(messageChunk.tool_call_chunks);
      } else if (messageChunk.content) {
        // TODO: 简化工具调用输出
        yield messageChunk.content;
      }
    }
  }

  /**
   * Handle tool call chunks for streaming tool call information
   */
  private async *handleToolCallChunks(toolCallChunks: ToolCallChunk[]): AsyncGenerator<string> {
    for (const chunk of toolCallChunks) {
      if (chunk.name) {
        yield `\n\n🔧 调用工具: ${chunk.name}`;
        if (chunk.args) {
          try {
            const args = typeof chunk.args === 'string' ? JSON.parse(chunk.args) : chunk.args;
            yield `\n📥 参数: ${JSON.stringify(args, null, 2)}`;
          } catch {
            yield `\n📥 参数: ${chunk.args}`;
          }
        }
      }
    }
  }

  async processMessage(message: string, sessionId?: string): Promise<string> {
    let fullResponse = '';
    for await (const chunk of this.streamMessage(message, sessionId)) {
      fullResponse += chunk;
    }
    return fullResponse;
  }

  /**
   * 获取当前会话状态
   */
  async getSessionState(sessionId: string): Promise<AgentStateType | null> {
    if (!this.checkpointer || !this.compiledWorkflow) {
      return null;
    }

    try {
      const state = await this.compiledWorkflow.getState({ 
        configurable: { thread_id: sessionId } 
      });
      return state?.values || null;
    } catch (error) {
      logger.debug('[HaiCodeAgent] Could not retrieve session state:', { error: error instanceof Error ? error.message : String(error) });
      return null;
    }
  }

  /**
   * 清除会话状态
   */
  async clearSessionState(sessionId: string): Promise<void> {
    if (!this.checkpointer || !this.compiledWorkflow) {
      return;
    }

    try {
      await this.compiledWorkflow.clearState({ 
        configurable: { thread_id: sessionId } 
      });
      logger.debug(`[HaiCodeAgent] Cleared session state for: ${sessionId}`);
    } catch (error) {
      logger.error('[HaiCodeAgent] Failed to clear session state:', { error: error instanceof Error ? error.message : String(error) });
    }
  }

  /**
   * 获取 Langfuse 配置状态
   */
  getLangfuseStatus(): { enabled: boolean; hasHandler: boolean } {
    return {
      enabled: getLangfuseHelper().isEnabled(),
      hasHandler: this.langfuseHandler !== null,
    };
  }

  /**
   * 刷新 Langfuse 事件（确保发送到服务器）
   */
  async flushLangfuse(): Promise<void> {
    if (this.langfuseHandler) {
      try {
        await this.langfuseHandler.flushAsync();
      } catch (error) {
        logger.error('刷新 Langfuse 事件失败', { error: error instanceof Error ? error.message : String(error) });
      }
    }
  }

  /**
   * 关闭 Langfuse 连接
   */
  async shutdownLangfuse(): Promise<void> {
    if (this.langfuseHandler) {
      try {
        await this.langfuseHandler.shutdownAsync();
      } catch (error) {
        logger.error('关闭 Langfuse 连接失败', { error: error instanceof Error ? error.message : String(error) });
      }
    }
  }

  /**
   * 更新系统提示词
   * 清除缓存以确保下次使用新的提示词
   */
  updateSystemPrompt(prompt: string): void {
    this.config.systemPrompt = prompt;
    this.systemPromptCache = null; // 清除缓存
    // 重新编译工作流以使用新的提示词
    this.compiledWorkflow = this.initializeWorkflow();
  }

  /**
   * 更新用户记忆
   * 清除缓存以确保下次使用新的记忆
   */
  updateUserMemory(memory: string): void {
    this.config.userMemory = memory;
    this.systemPromptCache = null; // 清除缓存
    // 重新编译工作流以使用新的记忆
    this.compiledWorkflow = this.initializeWorkflow();
  }

  /**
   * 获取当前系统提示词
   * 使用缓存提高性能，遵循 LangGraph.js 最佳实践
   */
  getCurrentSystemPrompt(): string {
    if (this.systemPromptCache) {
      return this.systemPromptCache;
    }

    let prompt: string;
    if (this.config.systemPrompt) {
      // 如果有自定义系统提示词，使用它作为基础
      // 根据 LangGraph 最佳实践，在自定义提示词中也要明确提及工具
      prompt = this.config.systemPrompt;

      // 如果有用户记忆，添加到提示词后面
      if (this.config.userMemory && this.config.userMemory.trim().length > 0) {
        prompt += `\n\n---\n\n${this.config.userMemory.trim()}`;
      }
    } else {
      // 使用默认系统提示词生成函数
      prompt = getHaiAgentSystemPrompt(this.config.model, this.config.userMemory);
    }

    this.systemPromptCache = prompt;
    return prompt;
  }

  /**
   * 重置为默认系统提示词
   */
  resetSystemPrompt(): void {
    this.config.systemPrompt = undefined;
    this.systemPromptCache = null; // 清除缓存
    this.compiledWorkflow = this.initializeWorkflow();
  }

  /**
   * 获取工具信息，用于系统提示词生成
   */
  getToolsInfo(): Array<{ name: string; description: string }> {
    return this.allTools.map(tool => ({
      name: tool.name,
      description: tool.description
    }));
  }

  /**
   * 获取 MCP 状态信息
   */
  getMCPStatus(): {
    enabled: boolean;
    initialized: boolean;
    serverCount: number;
    toolCount: number;
    servers: string[];
  } | null {
    return this.mcpClient ? this.mcpClient.getStatus() : null;
  }

  /**
   * 更新 MCP 配置
   */
  async updateMCPConfig(mcpConfig: HaiAgentMCPConfig): Promise<void> {
    this.config.mcp = mcpConfig;

    // 关闭现有 MCP 客户端
    if (this.mcpClient) {
      await this.mcpClient.close();
    }

    // 重新初始化 MCP
    this.initializeMCP();

    // 重新初始化工作流
    this.compiledWorkflow = null;
    await this.ensureWorkflowInitialized();
  }

  /**
   * 关闭 MCP 连接
   */
  async closeMCP(): Promise<void> {
    if (this.mcpClient) {
      await this.mcpClient.close();
      this.mcpClient = null;
    }
  }

  /**
   * 关闭所有连接
   */
  async close(): Promise<void> {
    await Promise.all([
      this.flushLangfuse(),
      this.shutdownLangfuse(),
      this.closeMCP(),
    ]);
  }
}

// Export MCP functionality
export type {
  HaiAgentMCPConfig,
  MCPServerConfig,
  MCPServerStdioConfig,
  MCPServerHttpConfig,
} from './config/mcp.js';

export {
  PREDEFINED_MCP_SERVERS,
  DEFAULT_MCP_CONFIG,
  createMCPServerConfig,
  validateMCPServerConfig,
} from './config/mcp.js';

export {
  HaiAgentMCPClient,
  createMCPClient,
  testMCPServerConnection,
} from './mcp/client.js';

