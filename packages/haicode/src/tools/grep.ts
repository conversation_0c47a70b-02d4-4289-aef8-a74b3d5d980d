import { tool } from "@langchain/core/tools";
import { z } from "zod";
import fs from 'fs';
import fsPromises from 'fs/promises';
import path from 'path';
import { EOL } from 'os';
import { spawn } from 'child_process';
import { globStream } from 'glob';

// --- Interfaces ---

/**
 * Parameters for the GrepTool
 */
export interface GrepToolParams {
  /**
   * The regular expression pattern to search for in file contents
   */
  pattern: string;

  /**
   * The directory to search in (optional, defaults to current directory relative to root)
   */
  path?: string | null;

  /**
   * File pattern to include in the search (e.g. "*.js", "*.{ts,tsx}")
   */
  include?: string | null;
}

/**
 * Result object for a single grep match
 */
interface GrepMatch {
  filePath: string;
  lineNumber: number;
  line: string;
}

// --- Utility Functions ---

/**
 * Checks if a path is within the root directory and resolves it.
 * @param relativePath Path relative to the root directory (or undefined for root).
 * @param targetDir The target directory to resolve against
 * @returns The absolute path if valid and exists.
 * @throws {Error} If path is outside root, doesn't exist, or isn't a directory.
 */
function resolveAndValidatePath(relativePath?: string | null, targetDir?: string): string {
  const rootDir = targetDir || process.cwd();
  const targetPath = path.resolve(rootDir, relativePath || '.');

  // Security Check: Ensure the resolved path is still within the root directory.
  if (
    !targetPath.startsWith(rootDir) &&
    targetPath !== rootDir
  ) {
    throw new Error(
      `Path validation failed: Attempted path "${relativePath || '.'}" resolves outside the allowed root directory "${rootDir}".`,
    );
  }

  // Check existence and type after resolving
  try {
    const stats = fs.statSync(targetPath);
    if (!stats.isDirectory()) {
      throw new Error(`Path is not a directory: ${targetPath}`);
    }
  } catch (error: unknown) {
    if (error instanceof Error && 'code' in error && error.code !== 'ENOENT') {
      throw new Error(`Path does not exist: ${targetPath}`);
    }
    throw new Error(
      `Failed to access path stats for ${targetPath}: ${error}`,
    );
  }

  return targetPath;
}

/**
 * Validates the parameters for the tool
 * @param params Parameters to validate
 * @returns An error message string if invalid, null otherwise
 */
function validateToolParams(params: GrepToolParams): string | null {
  if (!params.pattern || typeof params.pattern !== 'string' || params.pattern.trim() === '') {
    return "The 'pattern' parameter cannot be empty.";
  }

  try {
    new RegExp(params.pattern);
  } catch (error) {
    return `Invalid regular expression pattern provided: ${params.pattern}. Error: ${error instanceof Error ? error.message : String(error)}`;
  }

  try {
    resolveAndValidatePath(params.path);
  } catch (error) {
    return error instanceof Error ? error.message : String(error);
  }

  return null; // Parameters are valid
}

/**
 * Checks if a command is available in the system's PATH.
 * @param {string} command The command name (e.g., 'git', 'grep').
 * @returns {Promise<boolean>} True if the command is available, false otherwise.
 */
function isCommandAvailable(command: string): Promise<boolean> {
  return new Promise((resolve) => {
    const checkCommand = process.platform === 'win32' ? 'where' : 'command';
    const checkArgs =
      process.platform === 'win32' ? [command] : ['-v', command];
    try {
      const child = spawn(checkCommand, checkArgs, {
        stdio: 'ignore',
        shell: process.platform === 'win32',
      });
      child.on('close', (code) => resolve(code === 0));
      child.on('error', () => resolve(false));
    } catch {
      resolve(false);
    }
  });
}

/**
 * Parses the standard output of grep-like commands (git grep, system grep).
 * Expects format: filePath:lineNumber:lineContent
 * Handles colons within file paths and line content correctly.
 * @param {string} output The raw stdout string.
 * @param {string} basePath The absolute directory the search was run from, for relative paths.
 * @returns {GrepMatch[]} Array of match objects.
 */
function parseGrepOutput(output: string, basePath: string): GrepMatch[] {
  const results: GrepMatch[] = [];
  if (!output) return results;

  const lines = output.split(EOL); // Use OS-specific end-of-line

  for (const line of lines) {
    if (!line.trim()) continue;

    // Find the index of the first colon.
    const firstColonIndex = line.indexOf(':');
    if (firstColonIndex === -1) continue; // Malformed

    // Find the index of the second colon, searching *after* the first one.
    const secondColonIndex = line.indexOf(':', firstColonIndex + 1);
    if (secondColonIndex === -1) continue; // Malformed

    // Extract parts based on the found colon indices
    const filePathRaw = line.substring(0, firstColonIndex);
    const lineNumberStr = line.substring(
      firstColonIndex + 1,
      secondColonIndex,
    );
    const lineContent = line.substring(secondColonIndex + 1);

    const lineNumber = parseInt(lineNumberStr, 10);

    if (!isNaN(lineNumber)) {
      const absoluteFilePath = path.resolve(basePath, filePathRaw);
      const relativeFilePath = path.relative(basePath, absoluteFilePath);

      results.push({
        filePath: relativeFilePath || path.basename(absoluteFilePath),
        lineNumber,
        line: lineContent,
      });
    }
  }
  return results;
}

/**
 * Checks if a directory is a git repository
 * @param dirPath The directory path to check
 * @returns True if it's a git repository
 */
function isGitRepository(dirPath: string): boolean {
  try {
    const gitDir = path.join(dirPath, '.git');
    return fs.existsSync(gitDir);
  } catch {
    return false;
  }
}

/**
 * Performs the actual search using the prioritized strategies.
 * @param options Search options including pattern, absolute path, and include glob.
 * @returns A promise resolving to an array of match objects.
 */
async function performGrepSearch(options: {
  pattern: string;
  path: string; // Expects absolute path
  include?: string | null;
}): Promise<GrepMatch[]> {
  const { pattern, path: absolutePath, include } = options;
  let strategyUsed = 'none';

  try {
    // --- Strategy 1: git grep ---
    const isGit = isGitRepository(absolutePath);
    const gitAvailable = isGit && (await isCommandAvailable('git'));

    if (gitAvailable) {
      strategyUsed = 'git grep';
      const gitArgs = [
        'grep',
        '--untracked',
        '-n',
        '-E',
        '--ignore-case',
        pattern,
      ];
      if (include) {
        gitArgs.push('--', include);
      }

      try {
        const output = await new Promise<string>((resolve, reject) => {
          const child = spawn('git', gitArgs, {
            cwd: absolutePath,
            windowsHide: true,
          });
          const stdoutChunks: Buffer[] = [];
          const stderrChunks: Buffer[] = [];

          child.stdout.on('data', (chunk) => stdoutChunks.push(chunk));
          child.stderr.on('data', (chunk) => stderrChunks.push(chunk));
          child.on('error', (err) =>
            reject(new Error(`Failed to start git grep: ${err.message}`)),
          );
          child.on('close', (code) => {
            const stdoutData = Buffer.concat(stdoutChunks).toString('utf8');
            const stderrData = Buffer.concat(stderrChunks).toString('utf8');
            if (code === 0) resolve(stdoutData);
            else if (code === 1)
              resolve(''); // No matches
            else
              reject(
                new Error(`git grep exited with code ${code}: ${stderrData}`),
              );
          });
        });
        return parseGrepOutput(output, absolutePath);
      } catch (gitError: unknown) {
        console.debug(
          `GrepTool: git grep failed: ${gitError instanceof Error ? gitError.message : String(gitError)}. Falling back...`,
        );
      }
    }

    // --- Strategy 2: System grep ---
    const grepAvailable = await isCommandAvailable('grep');
    if (grepAvailable) {
      strategyUsed = 'system grep';
      const grepArgs = ['-r', '-n', '-H', '-E'];
      const commonExcludes = ['.git', 'node_modules', 'bower_components'];
      commonExcludes.forEach((dir) => grepArgs.push(`--exclude-dir=${dir}`));
      if (include) {
        grepArgs.push(`--include=${include}`);
      }
      grepArgs.push(pattern);
      grepArgs.push('.');

      try {
        const output = await new Promise<string>((resolve, reject) => {
          const child = spawn('grep', grepArgs, {
            cwd: absolutePath,
            windowsHide: true,
          });
          const stdoutChunks: Buffer[] = [];
          const stderrChunks: Buffer[] = [];

          const onData = (chunk: Buffer) => stdoutChunks.push(chunk);
          const onStderr = (chunk: Buffer) => {
            const stderrStr = chunk.toString();
            // Suppress common harmless stderr messages
            if (
              !stderrStr.includes('Permission denied') &&
              !/grep:.*: Is a directory/i.test(stderrStr)
            ) {
              stderrChunks.push(chunk);
            }
          };
          const onError = (err: Error) => {
            cleanup();
            reject(new Error(`Failed to start system grep: ${err.message}`));
          };
          const onClose = (code: number | null) => {
            const stdoutData = Buffer.concat(stdoutChunks).toString('utf8');
            const stderrData = Buffer.concat(stderrChunks)
              .toString('utf8')
              .trim();
            cleanup();
            if (code === 0) resolve(stdoutData);
            else if (code === 1)
              resolve(''); // No matches
            else {
              if (stderrData)
                reject(
                  new Error(
                    `System grep exited with code ${code}: ${stderrData}`,
                  ),
                );
              else resolve(''); // Exit code > 1 but no stderr, likely just suppressed errors
            }
          };

          const cleanup = () => {
            child.stdout.removeListener('data', onData);
            child.stderr.removeListener('data', onStderr);
            child.removeListener('error', onError);
            child.removeListener('close', onClose);
            if (child.connected) {
              child.disconnect();
            }
          };

          child.stdout.on('data', onData);
          child.stderr.on('data', onStderr);
          child.on('error', onError);
          child.on('close', onClose);
        });
        return parseGrepOutput(output, absolutePath);
      } catch (grepError: unknown) {
        console.debug(
          `GrepTool: System grep failed: ${grepError instanceof Error ? grepError.message : String(grepError)}. Falling back...`,
        );
      }
    }

    // --- Strategy 3: Pure JavaScript Fallback ---
    console.debug(
      'GrepTool: Falling back to JavaScript grep implementation.',
    );
    strategyUsed = 'javascript fallback';
    const globPattern = include ? include : '**/*';
    const ignorePatterns = [
      '.git/**',
      'node_modules/**',
      'bower_components/**',
      '.svn/**',
      '.hg/**',
    ]; // Use glob patterns for ignores here

    const filesStream = globStream(globPattern, {
      cwd: absolutePath,
      dot: true,
      ignore: ignorePatterns,
      absolute: true,
      nodir: true,
    });

    const regex = new RegExp(pattern, 'i');
    const allMatches: GrepMatch[] = [];

    for await (const filePath of filesStream) {
      const fileAbsolutePath = filePath as string;
      try {
        const content = await fsPromises.readFile(fileAbsolutePath, 'utf8');
        const lines = content.split(/\r?\n/);
        lines.forEach((line, index) => {
          if (regex.test(line)) {
            allMatches.push({
              filePath:
                path.relative(absolutePath, fileAbsolutePath) ||
                path.basename(fileAbsolutePath),
              lineNumber: index + 1,
              line,
            });
          }
        });
      } catch (readError: unknown) {
        // Ignore errors like permission denied or file gone during read
        const isNodeError = (error: unknown): error is NodeJS.ErrnoException =>
          error instanceof Error && 'code' in error;
        if (!isNodeError(readError) || readError.code !== 'ENOENT') {
          console.debug(
            `GrepTool: Could not read/process ${fileAbsolutePath}: ${readError instanceof Error ? readError.message : String(readError)}`,
          );
        }
      }
    }

    return allMatches;
  } catch (error: unknown) {
    console.error(
      `GrepTool: Error in performGrepSearch (Strategy: ${strategyUsed}): ${error instanceof Error ? error.message : String(error)}`,
    );
    throw error; // Re-throw
  }
}

/**
 * Grep tool for langchain - searches for patterns in file contents
 * Implements the same core logic as packages/core/src/tools/grep.ts but follows langchainjs tool conventions
 */
export const grepTool = tool(
  async (params: GrepToolParams) => {
    const validationError = validateToolParams(params);
    if (validationError) {
      throw new Error(`Invalid parameters: ${validationError}`);
    }

    let searchDirAbs: string;
    try {
      searchDirAbs = resolveAndValidatePath(params.path);
      const searchDirDisplay = params.path || '.';

      const matches: GrepMatch[] = await performGrepSearch({
        pattern: params.pattern,
        path: searchDirAbs,
        include: params.include,
      });

      if (matches.length === 0) {
        const noMatchMsg = `No matches found for pattern "${params.pattern}" in path "${searchDirDisplay}"${params.include ? ` (filter: "${params.include}")` : ''}.`;
        return noMatchMsg;
      }

      const matchesByFile = matches.reduce(
        (acc, match) => {
          const relativeFilePath =
            path.relative(
              searchDirAbs,
              path.resolve(searchDirAbs, match.filePath),
            ) || path.basename(match.filePath);
          if (!acc[relativeFilePath]) {
            acc[relativeFilePath] = [];
          }
          acc[relativeFilePath].push(match);
          acc[relativeFilePath].sort((a, b) => a.lineNumber - b.lineNumber);
          return acc;
        },
        {} as Record<string, GrepMatch[]>,
      );

      const matchCount = matches.length;
      const matchTerm = matchCount === 1 ? 'match' : 'matches';

      let llmContent = `Found ${matchCount} ${matchTerm} for pattern "${params.pattern}" in path "${searchDirDisplay}"${params.include ? ` (filter: "${params.include}")` : ''}:\n---\n`;

      for (const filePath in matchesByFile) {
        llmContent += `File: ${filePath}\n`;
        matchesByFile[filePath].forEach((match) => {
          const trimmedLine = match.line.trim();
          llmContent += `L${match.lineNumber}: ${trimmedLine}\n`;
        });
        llmContent += '---\n';
      }

      return llmContent.trim();
    } catch (error) {
      console.error(`Error during GrepTool execution: ${error}`);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Error during grep search operation: ${errorMessage}`);
    }
  },
  {
    name: "search_file_content",
    description: "Searches for a regular expression pattern within the content of files in a specified directory (or current working directory). Can filter files by a glob pattern. Returns the lines containing matches, along with their file paths and line numbers.",
    schema: z.object({
      pattern: z.string().describe(
        "The regular expression (regex) pattern to search for within file contents (e.g., 'function\\s+myFunction', 'import\\s+\\{.*\\}\\s+from\\s+.*')."
      ),
      path: z.string().nullable().optional().describe(
        'Optional: The absolute path to the directory to search within. If omitted, searches the current working directory.'
      ),
      include: z.string().nullable().optional().describe(
        "Optional: A glob pattern to filter which files are searched (e.g., '*.js', '*.{ts,tsx}', 'src/**'). If omitted, searches all files (respecting potential global ignores)."
      ),
    }),
  }
);
