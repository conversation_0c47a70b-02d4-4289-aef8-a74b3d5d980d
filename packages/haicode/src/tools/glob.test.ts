import { describe, it, expect } from 'vitest';
import { globTool, sortFileEntries, isWithinRoot, makeRelative, shortenPath } from './glob.js';

describe('Glob Tool', () => {
  describe('Helper Functions', () => {
    it('should check if path is within root', () => {
      expect(isWithinRoot('/home/<USER>/project/file.ts', '/home/<USER>/project')).toBe(true);
      expect(isWithinRoot('/home/<USER>/project', '/home/<USER>/project')).toBe(true);
      expect(isWithinRoot('/home/<USER>/other/file.ts', '/home/<USER>/project')).toBe(false);
    });

    it('should make relative paths correctly', () => {
      expect(makeRelative('/home/<USER>/project/src/file.ts', '/home/<USER>/project')).toBe('src/file.ts');
      expect(makeRelative('/home/<USER>/project', '/home/<USER>/project')).toBe('.');
    });

    it('should shorten paths correctly', () => {
      const longPath = '/very/long/path/to/some/deeply/nested/file.ts';
      const shortened = shortenPath(longPath, 30);
      expect(shortened.length).toBeLessThanOrEqual(30);
      expect(shortened).toContain('...');
    });

    it('should sort file entries by recency', () => {
      const now = Date.now();
      const oneDayAgo = now - (24 * 60 * 60 * 1000);
      const twoDaysAgo = now - (2 * 24 * 60 * 60 * 1000);

      const entries = [
        { fullpath: () => '/path/old.ts', mtimeMs: twoDaysAgo },
        { fullpath: () => '/path/recent.ts', mtimeMs: oneDayAgo },
        { fullpath: () => '/path/newest.ts', mtimeMs: now },
      ];

      const sorted = sortFileEntries(entries, now, 25 * 60 * 60 * 1000); // 25 hours threshold
      expect(sorted[0].fullpath()).toBe('/path/newest.ts');
      expect(sorted[1].fullpath()).toBe('/path/recent.ts');
      expect(sorted[2].fullpath()).toBe('/path/old.ts');
    });
  });

  describe('Glob Tool Integration', () => {
    it('should validate required pattern parameter', async () => {
      await expect(globTool.invoke({ pattern: '' })).rejects.toThrow('pattern');
    });

    it('should find TypeScript files', async () => {
      const result = await globTool.invoke({ pattern: '**/*.ts' });
      expect(result).toContain('Found');
      expect(result).toContain('.ts');
    });

    it('should handle non-existent patterns gracefully', async () => {
      const result = await globTool.invoke({ pattern: '**/*.nonexistent' });
      expect(result).toContain('No files found');
    });

    it('should respect case sensitivity', async () => {
      // This test assumes there are .ts files but no .TS files
      const caseSensitive = await globTool.invoke({ 
        pattern: '**/*.TS', 
        case_sensitive: true 
      });
      expect(caseSensitive).toContain('No files found');
    });

    it('should work with specific directory paths', async () => {
      const result = await globTool.invoke({ 
        pattern: '*.ts',
        path: './src'
      });
      expect(result).toContain('Found');
    });
  });
});
