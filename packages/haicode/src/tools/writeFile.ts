import { tool } from "@langchain/core/tools";
import { z } from "zod";
import fs from 'fs';
import path from 'path';

/**
 * WriteFile tool for langchain - writes content to a specified file
 */
export const writeFileTool = tool(
  async (params) => {
    const { file_path, content } = params;
    
    try {
      // Validate file path
      if (!path.isAbsolute(file_path)) {
        throw new Error(`File path must be absolute, but was relative: ${file_path}. You must provide an absolute path.`);
      }
      
      // Ensure directory exists
      const directory = path.dirname(file_path);
      if (!fs.existsSync(directory)) {
        fs.mkdirSync(directory, { recursive: true });
      }
      
      // Check if file already exists
      const fileExists = fs.existsSync(file_path);
      
      // Write content to file
      fs.writeFileSync(file_path, content, 'utf-8');
      
      const relativePath = path.relative(process.cwd(), file_path);
      const action = fileExists ? 'Updated' : 'Created';
      const contentLength = content.length;
      const lineCount = content.split('\n').length;
      
      return `${action} file: ${relativePath}\nContent length: ${contentLength} characters, ${lineCount} lines`;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to write file: ${errorMessage}`);
    }
  },
  {
    name: "write_file",
    description: "Writes content to a specified file in the local filesystem. Creates the file if it doesn't exist, or overwrites it if it does.",
    schema: z.object({
      file_path: z.string().describe("The absolute path to the file to write to (e.g., '/home/<USER>/project/file.txt'). Relative paths are not supported."),
      content: z.string().describe("The content to write to the file."),
    }),
  }
);
