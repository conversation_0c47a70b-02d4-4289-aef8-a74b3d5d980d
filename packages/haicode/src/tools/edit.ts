import { tool } from "@langchain/core/tools";
import { z } from "zod";
import fs from 'fs';
import path from 'path';
import * as Diff from 'diff';

/**
 * Parameters for the Edit tool
 */
export interface EditToolParams {
  /**
   * The absolute path to the file to modify
   */
  file_path: string;

  /**
   * The text to replace
   */
  old_string: string;

  /**
   * The text to replace it with
   */
  new_string: string;

  /**
   * Number of replacements expected. Use null for single replacement (default).
   * Use when you want to replace multiple occurrences.
   */
  expected_replacements: number | null;
}

interface CalculatedEdit {
  currentContent: string | null;
  newContent: string;
  occurrences: number;
  error?: { display: string; raw: string };
  isNewFile: boolean;
}

/**
 * Validates the parameters for the Edit tool
 * @param params Parameters to validate
 * @returns Error message string or null if valid
 */
function validateToolParams(params: EditToolParams): string | null {
  if (!params.file_path || typeof params.file_path !== 'string') {
    return 'file_path is required and must be a string';
  }

  if (!path.isAbsolute(params.file_path)) {
    return `File path must be absolute: ${params.file_path}`;
  }

  if (typeof params.old_string !== 'string') {
    return 'old_string is required and must be a string';
  }

  if (typeof params.new_string !== 'string') {
    return 'new_string is required and must be a string';
  }

  if (params.expected_replacements !== null &&
      (typeof params.expected_replacements !== 'number' || params.expected_replacements < 1)) {
    return 'expected_replacements must be a positive number';
  }

  return null;
}

/**
 * Checks if a path is within the current working directory
 * @param pathToCheck The absolute path to check
 * @param rootDirectory The root directory (defaults to cwd)
 * @returns True if the path is within the root directory
 */
function isWithinRoot(pathToCheck: string, rootDirectory: string = process.cwd()): boolean {
  const normalizedPathToCheck = path.resolve(pathToCheck);
  const normalizedRootDirectory = path.resolve(rootDirectory);

  const rootWithSeparator =
    normalizedRootDirectory === path.sep ||
    normalizedRootDirectory.endsWith(path.sep)
      ? normalizedRootDirectory
      : normalizedRootDirectory + path.sep;

  return (
    normalizedPathToCheck === normalizedRootDirectory ||
    normalizedPathToCheck.startsWith(rootWithSeparator)
  );
}

/**
 * Applies replacement to content
 */
function applyReplacement(
  currentContent: string | null,
  oldString: string,
  newString: string,
  isNewFile: boolean,
): string {
  if (isNewFile) {
    return newString;
  }
  if (currentContent === null) {
    return oldString === '' ? newString : '';
  }
  if (oldString === '' && !isNewFile) {
    return currentContent;
  }
  return currentContent.replaceAll(oldString, newString);
}

/**
 * Calculates the potential outcome of an edit operation
 */
function calculateEdit(params: EditToolParams): CalculatedEdit {
  const expectedReplacements = params.expected_replacements ?? 1;
  let currentContent: string | null = null;
  let fileExists = false;
  let isNewFile = false;
  let occurrences = 0;
  let error: { display: string; raw: string } | undefined = undefined;

  try {
    currentContent = fs.readFileSync(params.file_path, 'utf8');
    // Normalize line endings to LF for consistent processing
    currentContent = currentContent.replace(/\r\n/g, '\n');
    fileExists = true;
  } catch (err: unknown) {
    if (err && typeof err === 'object' && 'code' in err && err.code === 'ENOENT') {
      fileExists = false;
    } else {
      // Rethrow unexpected FS errors (permissions, etc.)
      throw err;
    }
  }

  if (params.old_string === '' && !fileExists) {
    // Creating a new file
    isNewFile = true;
  } else if (!fileExists) {
    // Trying to edit a nonexistent file (and old_string is not empty)
    error = {
      display: `File not found. Cannot apply edit. Use an empty old_string to create a new file.`,
      raw: `File not found: ${params.file_path}`,
    };
  } else if (currentContent !== null) {
    // Editing an existing file
    occurrences = (currentContent.match(new RegExp(escapeRegExp(params.old_string), 'g')) || []).length;

    if (params.old_string === '') {
      // Error: Trying to create a file that already exists
      error = {
        display: `Failed to edit. Attempted to create a file that already exists.`,
        raw: `File already exists, cannot create: ${params.file_path}`,
      };
    } else if (occurrences === 0) {
      error = {
        display: `Failed to edit, could not find the string to replace.`,
        raw: `Failed to edit, 0 occurrences found for old_string in ${params.file_path}. No edits made. The exact text in old_string was not found. Ensure you're not escaping content incorrectly and check whitespace, indentation, and context.`,
      };
    } else if (occurrences !== expectedReplacements) {
      const occurrenceTerm = expectedReplacements === 1 ? 'occurrence' : 'occurrences';
      error = {
        display: `Failed to edit, expected ${expectedReplacements} ${occurrenceTerm} but found ${occurrences}.`,
        raw: `Failed to edit, Expected ${expectedReplacements} ${occurrenceTerm} but found ${occurrences} for old_string in file: ${params.file_path}`,
      };
    } else if (params.old_string === params.new_string) {
      error = {
        display: `No changes to apply. The old_string and new_string are identical.`,
        raw: `No changes to apply. The old_string and new_string are identical in file: ${params.file_path}`,
      };
    }
  } else {
    error = {
      display: `Failed to read content of file.`,
      raw: `Failed to read content of existing file: ${params.file_path}`,
    };
  }

  const newContent = applyReplacement(
    currentContent,
    params.old_string,
    params.new_string,
    isNewFile,
  );

  return {
    currentContent,
    newContent,
    occurrences,
    error,
    isNewFile,
  };
}

/**
 * Escapes special regex characters in a string
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Creates parent directories if they don't exist
 */
function ensureParentDirectoriesExist(filePath: string): void {
  const dirName = path.dirname(filePath);
  if (!fs.existsSync(dirName)) {
    fs.mkdirSync(dirName, { recursive: true });
  }
}

/**
 * Shortens a path string for display
 */
function shortenPath(filePath: string, maxLen: number = 35): string {
  if (filePath.length <= maxLen) {
    return filePath;
  }

  const keepLen = Math.floor((maxLen - 3) / 2);
  if (keepLen <= 0) {
    return filePath.substring(0, maxLen - 3) + '...';
  }

  const start = filePath.substring(0, keepLen);
  const end = filePath.substring(filePath.length - keepLen);
  return `${start}...${end}`;
}

/**
 * Makes a path relative to the current working directory
 */
function makeRelative(targetPath: string, rootDirectory: string = process.cwd()): string {
  const resolvedTargetPath = path.resolve(targetPath);
  const resolvedRootDirectory = path.resolve(rootDirectory);

  const relativePath = path.relative(resolvedRootDirectory, resolvedTargetPath);
  return relativePath || '.';
}

/**
 * Edit tool for langchain - replaces text within a file
 */
export const editTool = tool(
  async (params) => {
    const validationError = validateToolParams(params);
    if (validationError) {
      throw new Error(`Invalid parameters: ${validationError}`);
    }

    // Check if path is within current working directory for security
    if (!isWithinRoot(params.file_path)) {
      throw new Error(`File path must be within the current working directory: ${params.file_path}`);
    }

    let editData: CalculatedEdit;
    try {
      editData = calculateEdit(params);
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      throw new Error(`Error preparing edit: ${errorMsg}`);
    }

    if (editData.error) {
      throw new Error(editData.error.display);
    }

    try {
      ensureParentDirectoriesExist(params.file_path);
      fs.writeFileSync(params.file_path, editData.newContent, 'utf8');

      const relativePath = makeRelative(params.file_path);

      if (editData.isNewFile) {
        return `Created new file: ${shortenPath(relativePath)} with provided content.`;
      } else {
        // Generate diff for display
        const fileName = path.basename(params.file_path);
        const fileDiff = Diff.createPatch(
          fileName,
          editData.currentContent ?? '',
          editData.newContent,
          'Current',
          'Proposed',
        );

        const oldLines = params.old_string.split('\n').length;
        const newLines = params.new_string.split('\n').length;
        const lineDiff = newLines - oldLines;

        let result = `Successfully modified file: ${shortenPath(relativePath)} (${editData.occurrences} replacements).\n`;
        result += `Old text (${oldLines} lines): "${params.old_string.substring(0, 100)}${params.old_string.length > 100 ? '...' : ''}"\n`;
        result += `New text (${newLines} lines): "${params.new_string.substring(0, 100)}${params.new_string.length > 100 ? '...' : ''}"\n`;
        if (lineDiff !== 0) {
          result += `Line count change: ${lineDiff > 0 ? '+' : ''}${lineDiff}\n`;
        }
        result += `\nDiff:\n${fileDiff}`;

        return result;
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      throw new Error(`Error writing file: ${errorMsg}`);
    }
  },
  {
    name: "replace",
    description: `Replaces text within a file. By default, replaces a single occurrence, but can replace multiple occurrences when \`expected_replacements\` is specified. This tool requires providing significant context around the change to ensure precise targeting. Always examine the file's current content before attempting a text replacement.

The user has the ability to modify the \`new_string\` content. If modified, this will be stated in the response.

Expectation for required parameters:
1. \`file_path\` MUST be an absolute path; otherwise an error will be thrown.
2. \`old_string\` MUST be the exact literal text to replace (including all whitespace, indentation, newlines, and surrounding code etc.).
3. \`new_string\` MUST be the exact literal text to replace \`old_string\` with (also including all whitespace, indentation, newlines, and surrounding code etc.). Ensure the resulting code is correct and idiomatic.
4. NEVER escape \`old_string\` or \`new_string\`, that would break the exact literal text requirement.
**Important:** If ANY of the above are not satisfied, the tool will fail. CRITICAL for \`old_string\`: Must uniquely identify the single instance to change. Include at least 3 lines of context BEFORE and AFTER the target text, matching whitespace and indentation precisely. If this string matches multiple locations, or does not match exactly, the tool will fail.
**Multiple replacements:** Set \`expected_replacements\` to the number of occurrences you want to replace. The tool will replace ALL occurrences that match \`old_string\` exactly. Ensure the number of replacements matches your expectation.`,
    schema: z.object({
      file_path: z.string().describe("The absolute path to the file to modify. Must start with '/'."),
      old_string: z.string().describe("The exact literal text to replace, preferably unescaped. For single replacements (default), include at least 3 lines of context BEFORE and AFTER the target text, matching whitespace and indentation precisely. For multiple replacements, specify expected_replacements parameter. If this string is not the exact literal text (i.e. you escaped it) or does not match exactly, the tool will fail."),
      new_string: z.string().describe("The exact literal text to replace `old_string` with, preferably unescaped. Provide the EXACT text. Ensure the resulting code is correct and idiomatic."),
      expected_replacements: z.number().min(1).nullable().describe("Number of replacements expected. Use null for single replacement (default). Use a number when you want to replace multiple occurrences."),
    }),
  }
);
