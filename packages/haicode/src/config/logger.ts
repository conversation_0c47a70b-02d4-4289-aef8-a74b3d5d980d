/**
 * 日志配置管理
 * 提供统一的日志配置和环境变量支持
 */

import { LogLevel, type LoggerConfig } from '../utils/logger.js';

export interface LogConfig {
  level: LogLevel;
  enableDebug: boolean;
  enableColors: boolean;
  prefix: string;
}

/**
 * 从环境变量获取日志级别
 */
function getLogLevelFromEnv(): LogLevel {
  const level = process.env.HAI_CODE_LOG_LEVEL?.toLowerCase();
  
  switch (level) {
    case 'error':
      return LogLevel.ERROR;
    case 'warn':
      return LogLevel.WARN;
    case 'info':
      return LogLevel.INFO;
    case 'debug':
      return LogLevel.DEBUG;
    default:
      return LogLevel.INFO;
  }
}

/**
 * 从环境变量获取调试模式状态
 */
function getDebugModeFromEnv(): boolean {
  const debug = process.env.HAI_CODE_DEBUG?.toLowerCase();
  return debug === 'true' || debug === '1' || debug === 'yes';
}

/**
 * 从环境变量获取颜色支持状态
 */
function getColorSupportFromEnv(): boolean {
  const colors = process.env.HAI_CODE_COLORS?.toLowerCase();
  if (colors === 'false' || colors === '0' || colors === 'no') {
    return false;
  }
  if (colors === 'true' || colors === '1' || colors === 'yes') {
    return true;
  }
  // 默认根据终端类型决定
  return process.stdout.isTTY;
}

/**
 * 获取默认日志配置
 */
export function getDefaultLogConfig(): LogConfig {
  return {
    level: getLogLevelFromEnv(),
    enableDebug: getDebugModeFromEnv(),
    enableColors: getColorSupportFromEnv(),
    prefix: '[HaiCode]',
  };
}

/**
 * 获取日志器配置
 */
export function getLoggerConfig(): LoggerConfig {
  const config = getDefaultLogConfig();
  
  return {
    level: config.level,
    enableDebug: config.enableDebug,
    enableColors: config.enableColors,
    prefix: config.prefix,
  };
}

/**
 * 环境变量说明
 */
export const LOG_ENV_VARS = {
  HAI_CODE_LOG_LEVEL: '日志级别 (error|warn|info|debug)',
  HAI_CODE_DEBUG: '启用调试模式 (true|false)',
  HAI_CODE_COLORS: '启用颜色输出 (true|false)',
} as const;
