export const GEMINI_MODELS = ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-1.0-pro', 'gemini-pro', 'gemini-flash'];
export const HT_MODELS = ['ht::saas-deepseek-v3', 'ht::saas-deepseek-r1', 'ht::local-deepseek-r1', 'ht::saas-doubao-15-pro-32k', 'ht::saas-deepseek-r1-thinking', 'ht::qwen-25-14b-int4', 'ht::qwen-25-14b-int4-noft', 'ht::local-qwq-32b'];
export const OPENAI_MODELS = [...HT_MODELS, 'gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-4'];
export const ANTHROPIC_MODELS = ['claude-3-5-sonnet-20241022', 'claude-3-haiku-20240307', 'claude-3-opus-20240229'];

export const HT_EMBEDDING_MODEL = 'ht::bge-embedding';
export const HT_API_KEY = '123456789';

export const DEFAULT_OPENAI_API_KEY='sk-cITab2RRLmsfbUH-pNJg';
export const DEFAULT_OPENAI_BASE_URL='http://168.63.85.222/web/unauth/LLM_api_proxy/v1';
export const DEFAULT_HAI_CODE_MODEL='ht::saas-deepseek-v3';

export const DEFAULT_OPENAI_CONFIGS = {
  modelName: DEFAULT_HAI_CODE_MODEL,
  apiKey: DEFAULT_OPENAI_API_KEY,
  // temperature,
  // maxTokens: maxOutputTokens,
  streaming: true,
  configuration: {
    baseURL: DEFAULT_OPENAI_BASE_URL,
    // Disable telemetry and analytics to prevent network issues
    dangerouslyAllowBrowser: false,
    // Add timeout configuration
    timeout: 30000, // 30 seconds
  },
  // Disable structured outputs to avoid Zod compatibility issues
  strictTools: false,
  // Add timeout and retry configuration
  timeout: 30000, // 30 seconds
  maxRetries: 1, // Reduce retries to fail faster
  // Disable token counting for unknown models to prevent tiktoken errors
  modelKwargs: {
    // This helps avoid token counting issues with custom models
  },
};
