/**
 * Tests for MCP configuration types and utilities
 */

import { describe, it, expect } from 'vitest';
import {
  createMCPServerConfig,
  validateMCPServerConfig,
  PREDEFINED_MCP_SERVERS,
  DEFAULT_MCP_CONFIG,
  type MCPServerStdioConfig,
  type MCPServerHttpConfig,
} from './mcp.js';

describe('MCP Configuration', () => {
  describe('createMCPServerConfig', () => {
    it('should create stdio server config', () => {
      const config = createMCPServerConfig('stdio', {
        command: 'uvx',
        args: ['mcp-server-time'],
      });

      expect(config).toEqual({
        command: 'uvx',
        args: ['mcp-server-time'],
        transport: 'stdio',
        type: 'stdio',
      });
    });

    it('should create HTTP server config', () => {
      const config = createMCPServerConfig('http', {
        url: 'http://localhost:8000',
      });

      expect(config).toEqual({
        url: 'http://localhost:8000',
        transport: 'http',
        type: 'http',
      });
    });

    it('should create SSE server config', () => {
      const config = createMCPServerConfig('sse', {
        url: 'http://localhost:8000/sse',
        headers: {
          'Authorization': 'Bearer token',
        },
      });

      expect(config).toEqual({
        url: 'http://localhost:8000/sse',
        headers: {
          'Authorization': 'Bearer token',
        },
        transport: 'sse',
        type: 'sse',
      });
    });

    it('should include additional options', () => {
      const config = createMCPServerConfig('stdio', {
        command: 'python',
        args: ['server.py'],
        cwd: '/path/to/server',
        env: {
          'PYTHONPATH': '/custom/path',
        },
        defaultToolTimeout: 60000,
      });

      expect(config).toEqual({
        command: 'python',
        args: ['server.py'],
        cwd: '/path/to/server',
        env: {
          'PYTHONPATH': '/custom/path',
        },
        defaultToolTimeout: 60000,
        transport: 'stdio',
        type: 'stdio',
      });
    });
  });

  describe('validateMCPServerConfig', () => {
    it('should validate stdio config with required fields', () => {
      const config: MCPServerStdioConfig = {
        command: 'uvx',
        args: ['mcp-server-time'],
        transport: 'stdio',
      };

      expect(validateMCPServerConfig(config)).toBe(true);
    });

    it('should validate HTTP config with required fields', () => {
      const config: MCPServerHttpConfig = {
        url: 'http://localhost:8000',
        transport: 'http',
      };

      expect(validateMCPServerConfig(config)).toBe(true);
    });

    it('should reject stdio config without command', () => {
      const config = {
        args: ['mcp-server-time'],
        transport: 'stdio',
      } as MCPServerStdioConfig;

      expect(validateMCPServerConfig(config)).toBe(false);
    });

    it('should reject stdio config without args', () => {
      const config = {
        command: 'uvx',
        transport: 'stdio',
      } as MCPServerStdioConfig;

      expect(validateMCPServerConfig(config)).toBe(false);
    });

    it('should reject HTTP config without URL', () => {
      const config = {
        transport: 'http',
      } as MCPServerHttpConfig;

      expect(validateMCPServerConfig(config)).toBe(false);
    });

    it('should validate complex stdio config', () => {
      const config: MCPServerStdioConfig = {
        command: 'python',
        args: ['-m', 'server'],
        transport: 'stdio',
        cwd: '/path/to/server',
        env: {
          'PYTHONPATH': '/custom/path',
        },
        restart: {
          enabled: true,
          maxAttempts: 3,
          delayMs: 1000,
        },
        stderr: 'pipe',
        encoding: 'utf8',
        defaultToolTimeout: 45000,
      };

      expect(validateMCPServerConfig(config)).toBe(true);
    });

    it('should validate complex HTTP config', () => {
      const config: MCPServerHttpConfig = {
        url: 'https://api.example.com/mcp',
        transport: 'sse',
        headers: {
          'Authorization': 'Bearer token',
          'Content-Type': 'application/json',
        },
        reconnect: {
          enabled: true,
          maxAttempts: 5,
          delayMs: 2000,
        },
        automaticSSEFallback: true,
        defaultToolTimeout: 60000,
      };

      expect(validateMCPServerConfig(config)).toBe(true);
    });
  });

  describe('PREDEFINED_MCP_SERVERS', () => {
    it('should have time-server configuration', () => {
      const timeServer = PREDEFINED_MCP_SERVERS['time-server'];
      
      expect(timeServer).toBeDefined();
      expect(timeServer.command).toBe('uvx');
      expect(timeServer.args).toEqual(['mcp-server-time']);
      expect(timeServer.transport).toBe('stdio');
      expect(validateMCPServerConfig(timeServer)).toBe(true);
    });

    it('should have browser-tools configuration', () => {
      const browserTools = PREDEFINED_MCP_SERVERS['browser-tools'];
      
      expect(browserTools).toBeDefined();
      expect(browserTools.command).toBe('uvx');
      expect(browserTools.args).toEqual(['mcp-server-browser-tools']);
      expect(browserTools.transport).toBe('stdio');
      expect(validateMCPServerConfig(browserTools)).toBe(true);
    });

    it('should have filesystem configuration', () => {
      const filesystem = PREDEFINED_MCP_SERVERS['filesystem'];
      
      expect(filesystem).toBeDefined();
      expect(filesystem.command).toBe('uvx');
      expect(filesystem.args).toEqual(['mcp-server-filesystem']);
      expect(filesystem.transport).toBe('stdio');
      expect(validateMCPServerConfig(filesystem)).toBe(true);
    });

    it('should have all predefined servers valid', () => {
      Object.entries(PREDEFINED_MCP_SERVERS).forEach(([name, config]) => {
        expect(validateMCPServerConfig(config), `${name} should be valid`).toBe(true);
      });
    });
  });

  describe('DEFAULT_MCP_CONFIG', () => {
    it('should have correct default values', () => {
      expect(DEFAULT_MCP_CONFIG).toEqual({
        enabled: false,
        servers: {},
        settings: {
          prefixToolNameWithServerName: true,
          throwOnLoadError: false,
          defaultToolTimeout: 30000,
        },
      });
    });

    it('should be a valid configuration', () => {
      expect(DEFAULT_MCP_CONFIG.enabled).toBe(false);
      expect(DEFAULT_MCP_CONFIG.servers).toEqual({});
      expect(DEFAULT_MCP_CONFIG.settings).toBeDefined();
      expect(DEFAULT_MCP_CONFIG.settings?.prefixToolNameWithServerName).toBe(true);
      expect(DEFAULT_MCP_CONFIG.settings?.throwOnLoadError).toBe(false);
      expect(DEFAULT_MCP_CONFIG.settings?.defaultToolTimeout).toBe(30000);
    });
  });

  describe('Type Guards and Edge Cases', () => {
    it('should handle empty args array for stdio config', () => {
      const config: MCPServerStdioConfig = {
        command: 'echo',
        args: [],
        transport: 'stdio',
      };

      expect(validateMCPServerConfig(config)).toBe(true);
    });

    it('should handle URL with query parameters', () => {
      const config: MCPServerHttpConfig = {
        url: 'http://localhost:8000/sse?token=abc123',
        transport: 'sse',
      };

      expect(validateMCPServerConfig(config)).toBe(true);
    });

    it('should handle config with all optional fields', () => {
      const config: MCPServerStdioConfig = {
        command: 'python',
        args: ['server.py'],
        transport: 'stdio',
        cwd: '/path',
        encoding: 'utf8',
        env: { 'VAR': 'value' },
        restart: {
          enabled: true,
          maxAttempts: 3,
          delayMs: 1000,
        },
        stderr: 'pipe',
        defaultToolTimeout: 45000,
        outputHandling: 'content',
      };

      expect(validateMCPServerConfig(config)).toBe(true);
    });

    it('should handle config with complex output handling', () => {
      const config: MCPServerHttpConfig = {
        url: 'http://localhost:8000',
        transport: 'http',
        outputHandling: {
          text: 'content',
          image: 'artifact',
          audio: 'content',
          resource: 'artifact',
        },
      };

      expect(validateMCPServerConfig(config)).toBe(true);
    });
  });
});
