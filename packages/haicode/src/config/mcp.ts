/**
 * MCP (Model Context Protocol) configuration types and utilities
 * 
 * This module defines TypeScript types for configuring MCP servers
 * and provides utilities for managing MCP client connections.
 */

/**
 * Base configuration for MCP servers
 */
export interface MCPServerBaseConfig {
  /** Default timeout for tool execution in milliseconds */
  defaultToolTimeout?: number;
  /** How to handle different types of output */
  outputHandling?: "content" | "artifact" | {
    audio?: "content" | "artifact";
    image?: "content" | "artifact";
    resource?: "content" | "artifact";
    text?: "content" | "artifact";
  };
}

/**
 * Configuration for MCP servers using stdio transport
 */
export interface MCPServerStdioConfig extends MCPServerBaseConfig {
  /** Transport type */
  transport?: "stdio";
  type?: "stdio";
  /** Command to execute */
  command: string;
  /** Arguments to pass to the command */
  args: string[];
  /** Working directory for the command */
  cwd?: string;
  /** Character encoding */
  encoding?: string;
  /** Environment variables */
  env?: Record<string, string>;
  /** Restart configuration */
  restart?: {
    enabled?: boolean;
    maxAttempts?: number;
    delayMs?: number;
  };
  /** Standard error handling */
  stderr?: "overlapped" | "pipe" | "ignore" | "inherit";
}

/**
 * OAuth client provider for HTTP/SSE transport
 */
export interface OAuthClientProvider {
  // OAuth provider implementation details
  // This would be defined based on the specific OAuth implementation
  [key: string]: unknown;
}

/**
 * Configuration for MCP servers using HTTP/SSE transport
 */
export interface MCPServerHttpConfig extends MCPServerBaseConfig {
  /** Transport type */
  transport?: "http" | "sse";
  type?: "http" | "sse";
  /** Server URL */
  url: string;
  /** HTTP headers */
  headers?: Record<string, string>;
  /** OAuth authentication provider */
  authProvider?: OAuthClientProvider;
  /** Automatic fallback to SSE if HTTP fails */
  automaticSSEFallback?: boolean;
  /** Reconnection configuration */
  reconnect?: {
    enabled?: boolean;
    maxAttempts?: number;
    delayMs?: number;
  };
}

/**
 * Union type for all MCP server configurations
 */
export type MCPServerConfig = MCPServerStdioConfig | MCPServerHttpConfig;

/**
 * Configuration for multiple MCP servers
 */
export interface MCPMultiServerConfig extends MCPServerBaseConfig {
  /** Map of server names to their configurations */
  mcpServers: Record<string, MCPServerConfig>;
  /** Whether to prefix tool names with server name */
  prefixToolNameWithServerName?: boolean;
  /** Additional prefix for tool names */
  additionalToolNamePrefix?: string;
  /** Whether to throw on load error */
  throwOnLoadError?: boolean;
  /** Whether to use standard content blocks */
  useStandardContentBlocks?: boolean;
}

/**
 * MCP configuration for HaiCodeAgent
 */
export interface HaiAgentMCPConfig {
  /** Whether MCP is enabled */
  enabled?: boolean;
  /** MCP servers configuration */
  servers?: Record<string, MCPServerConfig>;
  /** Global MCP settings */
  settings?: {
    /** Whether to prefix tool names with server name */
    prefixToolNameWithServerName?: boolean;
    /** Additional prefix for tool names */
    additionalToolNamePrefix?: string;
    /** Whether to throw on load error */
    throwOnLoadError?: boolean;
    /** Default timeout for tool execution in milliseconds */
    defaultToolTimeout?: number;
  };
}

/**
 * Predefined MCP server configurations for common use cases
 */
export const PREDEFINED_MCP_SERVERS = {
  /**
   * Time server using uvx mcp-server-time
   * Example usage: uvx mcp-server-time
   */
  "time-server": {
    command: "uvx",
    args: ["mcp-server-time"],
    transport: "stdio" as const,
  },
  
  /**
   * Browser tools server
   * Example usage: uvx mcp-server-browser-tools
   */
  "browser-tools": {
    command: "uvx",
    args: ["mcp-server-browser-tools"],
    transport: "stdio" as const,
  },
  
  /**
   * File system server
   * Example usage: uvx mcp-server-filesystem
   */
  "filesystem": {
    command: "uvx",
    args: ["mcp-server-filesystem"],
    transport: "stdio" as const,
  },
} as const satisfies Record<string, MCPServerStdioConfig>;

/**
 * Utility function to create MCP server configuration
 */
export function createMCPServerConfig(
  type: "stdio",
  config: Omit<MCPServerStdioConfig, "transport" | "type">
): MCPServerStdioConfig;
export function createMCPServerConfig(
  type: "http" | "sse",
  config: Omit<MCPServerHttpConfig, "transport" | "type">
): MCPServerHttpConfig;
export function createMCPServerConfig(
  type: "stdio" | "http" | "sse",
  config: any
): MCPServerConfig {
  return {
    ...config,
    transport: type === "stdio" ? "stdio" : type,
    type: type === "stdio" ? "stdio" : type,
  };
}

/**
 * Utility function to validate MCP server configuration
 */
export function validateMCPServerConfig(config: MCPServerConfig): boolean {
  if ("command" in config) {
    // Stdio configuration
    return Boolean(config.command && Array.isArray(config.args));
  } else {
    // HTTP/SSE configuration
    return Boolean(config.url);
  }
}

/**
 * Default MCP configuration for HaiCodeAgent
 */
export const DEFAULT_MCP_CONFIG: HaiAgentMCPConfig = {
  enabled: false,
  servers: {},
  settings: {
    prefixToolNameWithServerName: true,
    throwOnLoadError: false,
    defaultToolTimeout: 30000, // 30 seconds
  },
};
