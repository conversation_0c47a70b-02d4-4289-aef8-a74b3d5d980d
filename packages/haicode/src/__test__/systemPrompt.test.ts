import { describe, it, expect, beforeEach } from 'vitest';
import { HaiCodeAgent } from '../index.js';
import { getHaiAgentSystemPrompt } from '../prompt.js';

describe('HaiCodeAgent System Prompt', () => {
  let agent: HaiCodeAgent;

  beforeEach(() => {
    agent = new HaiCodeAgent({
      model: 'gpt-3.5-turbo',
      apiKey: 'test-key'
    });
  });

  describe('getHaiAgentSystemPrompt', () => {
    it('should generate default system prompt with tool information', () => {
      const prompt = getHaiAgentSystemPrompt();

      expect(prompt).toContain('You are an interactive CLI agent');
      expect(prompt).toContain('User Information');
      expect(prompt).toContain('始终以中文回复');
    });

    it('should include user memory in system prompt', () => {
      const userMemory = 'User prefers TypeScript and uses VS Code';
      const prompt = getHaiAgentSystemPrompt(userMemory);

      expect(prompt).toContain(userMemory);
      expect(prompt).toContain('---');
    });

    it('should include environment information', () => {
      const prompt = getHaiAgentSystemPrompt();

      expect(prompt).toContain('## User Information');
      expect(prompt).toContain('OS version');
      expect(prompt).toContain('workspace');
    });
  });

  describe('HaiCodeAgent System Prompt Management', () => {
    it('should allow updating system prompt', () => {
      const customPrompt = 'Custom system prompt for testing';
      agent.updateSystemPrompt(customPrompt);

      const currentPrompt = agent.getCurrentSystemPrompt();
      expect(currentPrompt).toBe(customPrompt);
    });

    it('should allow updating user memory', () => {
      const userMemory = 'User likes clean code';
      agent.updateUserMemory(userMemory);

      const currentPrompt = agent.getCurrentSystemPrompt();
      expect(currentPrompt).toContain(userMemory);
    });

    it('should reset to default system prompt', () => {
      const customPrompt = 'Custom prompt';
      agent.updateSystemPrompt(customPrompt);

      // Verify custom prompt is set
      expect(agent.getCurrentSystemPrompt()).toBe(customPrompt);

      // Reset and verify default prompt is restored
      agent.resetSystemPrompt();
      const defaultPrompt = agent.getCurrentSystemPrompt();
      expect(defaultPrompt).toContain('You are an interactive CLI agent');
      expect(defaultPrompt).not.toBe(customPrompt);
    });

    it('should cache system prompt for performance', () => {
      // First call should generate and cache
      const prompt1 = agent.getCurrentSystemPrompt();

      // Second call should return cached version
      const prompt2 = agent.getCurrentSystemPrompt();

      expect(prompt1).toBe(prompt2);
    });

    it('should clear cache when updating system prompt', () => {
      // Get initial prompt (creates cache)
      const initialPrompt = agent.getCurrentSystemPrompt();

      // Update system prompt (should clear cache)
      const customPrompt = 'New custom prompt';
      agent.updateSystemPrompt(customPrompt);

      // Get new prompt (should be different)
      const newPrompt = agent.getCurrentSystemPrompt();

      expect(newPrompt).not.toBe(initialPrompt);
      expect(newPrompt).toBe(customPrompt);
    });

    it('should clear cache when updating user memory', () => {
      // Get initial prompt (creates cache)
      const initialPrompt = agent.getCurrentSystemPrompt();

      // Update user memory (should clear cache)
      agent.updateUserMemory('New user memory');

      // Get new prompt (should be different)
      const newPrompt = agent.getCurrentSystemPrompt();

      expect(newPrompt).not.toBe(initialPrompt);
      expect(newPrompt).toContain('New user memory');
    });
  });
});
