# Hai Code Agent 环境变量配置示例

# OpenAI 兼容 API 配置
OPENAI_API_KEY=your-api-key-here
OPENAI_BASE_URL=http://168.63.85.222/web/unauth/LLM_api_proxy/v1

# 默认模型
HAI_CODE_MODEL=ht::saas-deepseek-v3

# Langfuse 可观测性配置
# 启用/禁用 Langfuse (默认: true)
LANGFUSE_ENABLED=true

# Langfuse 服务器配置
LANGFUSE_PUBLIC_KEY=pk-lf-your-public-key-here
LANGFUSE_SECRET_KEY=sk-lf-your-secret-key-here
LANGFUSE_BASEURL=https://cloud.langfuse.com

# 可选的 Langfuse 配置
LANGFUSE_RELEASE=1.0.0
LANGFUSE_VERSION=hai-code-0.1.0

# 测试环境示例
# LANGFUSE_PUBLIC_KEY=xxxxxx
# LANGFUSE_SECRET_KEY=xxxxxx
# LANGFUSE_BASEURL=http://************:8090/fst-observe-pipeline/connect/langfuse
