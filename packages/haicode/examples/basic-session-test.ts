import { HaiCodeAgent } from '../src/index.js';

async function basicSessionTest() {
  console.log('🧪 基本会话测试...\n');

  // 创建代理实例
  const agent = new HaiCodeAgent({
    enablePersistence: true,
    systemPrompt: '你是一个有用的助手。',
  });

  const sessionId = 'test-session-456';

  try {
    // 第一次对话
    console.log('📝 第一次对话:');
    const response1 = await agent.processMessage('你好', sessionId);
    console.log('AI回复:', response1.substring(0, 50) + '...');
    console.log('');

    // 第二次对话
    console.log('📝 第二次对话:');
    const response2 = await agent.processMessage('请记住我说的话', sessionId);
    console.log('AI回复:', response2.substring(0, 50) + '...');
    console.log('');

    // 第三次对话 - 测试记忆
    console.log('📝 第三次对话 (测试记忆):');
    const response3 = await agent.processMessage('你还记得我之前说的话吗？', sessionId);
    console.log('AI回复:', response3.substring(0, 100) + '...');
    console.log('');

    console.log('✅ 基本会话测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await agent.close();
  }
}

basicSessionTest().catch(console.error);
