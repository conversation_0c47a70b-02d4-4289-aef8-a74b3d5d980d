/**
 * MCP (Model Context Protocol) Usage Examples for HaiCodeAgent
 * 
 * This file demonstrates how to configure and use MCP servers with HaiCodeAgent.
 * MCP allows the agent to use tools from external servers, extending its capabilities.
 */

import { HaiCodeAgent } from '../src/index.js';
import type { HaiAgentMCPConfig } from '../src/config/mcp.js';
import { PREDEFINED_MCP_SERVERS } from '../src/config/mcp.js';

/**
 * Example 1: Basic MCP configuration with time server
 */
async function basicMCPExample() {
  console.log('=== Basic MCP Example ===');
  
  const agent = new HaiCodeAgent({
    mcp: {
      enabled: true,
      servers: {
        "time-server": PREDEFINED_MCP_SERVERS["time-server"],
      },
      settings: {
        prefixToolNameWithServerName: true,
        throwOnLoadError: false,
        defaultToolTimeout: 30000,
      },
    },
  });

  try {
    // Get MCP status
    const mcpStatus = agent.getMCPStatus();
    console.log('MCP Status:', mcpStatus);

    // Ask the agent to use time-related tools
    const response = await agent.processMessage("What time is it now?");
    console.log('Agent Response:', response);
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await agent.close();
  }
}

/**
 * Example 2: Multiple MCP servers configuration
 */
async function multipleMCPServersExample() {
  console.log('\n=== Multiple MCP Servers Example ===');
  
  const mcpConfig: HaiAgentMCPConfig = {
    enabled: true,
    servers: {
      "time-server": {
        command: "uvx",
        args: ["mcp-server-time"],
        transport: "stdio",
      },
      "browser-tools": {
        command: "uvx", 
        args: ["mcp-server-browser-tools"],
        transport: "stdio",
      },
    },
    settings: {
      prefixToolNameWithServerName: true,
      additionalToolNamePrefix: "mcp_",
      throwOnLoadError: false,
      defaultToolTimeout: 45000,
    },
  };

  const agent = new HaiCodeAgent({
    mcp: mcpConfig,
  });

  try {
    // Get available tools
    const toolsInfo = agent.getToolsInfo();
    console.log('Available Tools:', toolsInfo.map(t => t.name));

    // Use multiple MCP tools
    const response = await agent.processMessage(
      "What time is it and can you search for information about TypeScript?"
    );
    console.log('Agent Response:', response);
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await agent.close();
  }
}

/**
 * Example 3: HTTP/SSE MCP server configuration
 */
async function httpMCPServerExample() {
  console.log('\n=== HTTP MCP Server Example ===');
  
  const agent = new HaiCodeAgent({
    mcp: {
      enabled: true,
      servers: {
        "remote-server": {
          url: "http://localhost:8000/sse",
          transport: "sse",
          headers: {
            "Authorization": "Bearer your-token-here",
          },
          reconnect: {
            enabled: true,
            maxAttempts: 3,
            delayMs: 1000,
          },
        },
      },
      settings: {
        prefixToolNameWithServerName: true,
        throwOnLoadError: false,
      },
    },
  });

  try {
    const mcpStatus = agent.getMCPStatus();
    console.log('MCP Status:', mcpStatus);

    // Note: This example requires a running MCP server on localhost:8000
    const response = await agent.processMessage("Hello from HTTP MCP server!");
    console.log('Agent Response:', response);
  } catch (error) {
    console.error('Error (expected if no server running):', error);
  } finally {
    await agent.close();
  }
}

/**
 * Example 4: Dynamic MCP configuration update
 */
async function dynamicMCPConfigExample() {
  console.log('\n=== Dynamic MCP Configuration Example ===');
  
  // Start with no MCP
  const agent = new HaiCodeAgent({
    mcp: {
      enabled: false,
    },
  });

  try {
    console.log('Initial MCP Status:', agent.getMCPStatus());

    // Enable MCP dynamically
    await agent.updateMCPConfig({
      enabled: true,
      servers: {
        "time-server": PREDEFINED_MCP_SERVERS["time-server"],
      },
      settings: {
        prefixToolNameWithServerName: true,
        throwOnLoadError: false,
      },
    });

    console.log('Updated MCP Status:', agent.getMCPStatus());

    const response = await agent.processMessage("What time is it?");
    console.log('Agent Response:', response);
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await agent.close();
  }
}

/**
 * Example 5: Custom MCP server configuration
 */
async function customMCPServerExample() {
  console.log('\n=== Custom MCP Server Example ===');
  
  const agent = new HaiCodeAgent({
    mcp: {
      enabled: true,
      servers: {
        "custom-math": {
          command: "python",
          args: ["/path/to/your/math_server.py"],
          transport: "stdio",
          cwd: "/path/to/server/directory",
          env: {
            "PYTHONPATH": "/path/to/python/modules",
          },
          restart: {
            enabled: true,
            maxAttempts: 3,
            delayMs: 2000,
          },
        },
      },
      settings: {
        prefixToolNameWithServerName: false,
        throwOnLoadError: true, // Fail fast for custom servers
        defaultToolTimeout: 60000, // Longer timeout for custom operations
      },
    },
  });

  try {
    const response = await agent.processMessage("Calculate 15 * 23 + 7");
    console.log('Agent Response:', response);
  } catch (error) {
    console.error('Error (expected if custom server not available):', error);
  } finally {
    await agent.close();
  }
}

/**
 * Run all examples
 */
async function runAllExamples() {
  try {
    await basicMCPExample();
    await multipleMCPServersExample();
    await httpMCPServerExample();
    await dynamicMCPConfigExample();
    await customMCPServerExample();
  } catch (error) {
    console.error('Failed to run examples:', error);
  }
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples();
}

export {
  basicMCPExample,
  multipleMCPServersExample,
  httpMCPServerExample,
  dynamicMCPConfigExample,
  customMCPServerExample,
  runAllExamples,
};
