import { HaiCodeAgent } from '../src/index.js';

async function simpleTest() {
  console.log('🧪 简单会话上下文测试...\n');

  const agent = new HaiCodeAgent({
    enablePersistence: true,
    systemPrompt: '你是一个有用的助手。请记住我们之前的对话内容。',
  });

  const sessionId = 'test-123';

  try {
    // 第一次对话
    console.log('📝 第一次对话:');
    const response1 = await agent.processMessage('你好，我的名字是张三', sessionId);
    console.log('AI回复:', response1.substring(0, 100) + '...');
    console.log('');

    // 第二次对话
    console.log('📝 第二次对话:');
    const response2 = await agent.processMessage('你还记得我的名字吗？', sessionId);
    console.log('AI回复:', response2.substring(0, 100) + '...');
    console.log('');

    // 检查会话状态
    console.log('🔍 检查会话状态:');
    const state = await agent.getSessionState(sessionId);
    console.log('会话状态:', state ? `消息数量: ${state.messages.length}` : '无状态');
    
    if (state && state.messages.length > 0) {
      console.log('✅ 会话上下文保持成功！');
    } else {
      console.log('❌ 会话上下文丢失！');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await agent.close();
  }
}

simpleTest().catch(console.error);
