import { HaiCodeAgent } from '../src/index.js';
import { getUserInfo } from '../src/utils/user.js';
import { getUUID } from '../src/utils/uuid.js';
import { getVersion } from '../src/utils/version.js';

async function testCliSessionContext() {
  console.log('🧪 测试 CLI 会话上下文修复...\n');

  const sessionId = 'cli-test-session-789';
  const userInfo = getUserInfo();

  // 创建代理实例，模拟 CLI 配置
  const agent = new HaiCodeAgent({
    model: 'ht::saas-deepseek-v3',
    enablePersistence: true,
    langfuse: {
      enabled: false, // 测试时禁用 Langfuse
      userId: userInfo.userName,
      sessionId, // 使用相同的 sessionId
      version: getVersion(),
    },
  });

  try {
    // 第一次对话
    console.log('📝 第一次对话:');
    const response1 = await agent.processMessage('你好，我的名字是李四', sessionId);
    console.log('AI回复:', response1.substring(0, 100) + '...');
    console.log('');

    // 检查会话状态
    console.log('🔍 检查会话状态:');
    const state1 = await agent.getSessionState(sessionId);
    console.log('会话状态:', state1 ? `消息数量: ${state1.messages.length}` : '无状态');
    console.log('');

    // 第二次对话 - 应该能记住之前的对话
    console.log('📝 第二次对话:');
    const response2 = await agent.processMessage('你还记得我的名字吗？', sessionId);
    console.log('AI回复:', response2.substring(0, 100) + '...');
    console.log('');

    // 再次检查会话状态
    console.log('🔍 再次检查会话状态:');
    const state2 = await agent.getSessionState(sessionId);
    console.log('会话状态:', state2 ? `消息数量: ${state2.messages.length}` : '无状态');
    console.log('');

    // 第三次对话 - 测试工具调用
    console.log('📝 第三次对话 (测试工具调用):');
    const response3 = await agent.processMessage('请列出当前目录的文件', sessionId);
    console.log('AI回复:', response3.substring(0, 100) + '...');
    console.log('');

    // 最终检查会话状态
    console.log('🔍 最终会话状态:');
    const finalState = await agent.getSessionState(sessionId);
    console.log('会话状态:', finalState ? `消息数量: ${finalState.messages.length}` : '无状态');
    
    if (finalState) {
      console.log('消息历史:');
      finalState.messages.forEach((msg, index) => {
        const role = msg.constructor.name;
        const content = typeof msg.content === 'string' ? msg.content.substring(0, 50) + '...' : '复杂内容';
        console.log(`  ${index + 1}. [${role}] ${content}`);
      });
    }

    // 测试 Langfuse sessionId 一致性
    console.log('\n🔍 测试 Langfuse sessionId 一致性:');
    const langfuseStatus = agent.getLangfuseStatus();
    console.log('Langfuse 状态:', langfuseStatus);
    
    if (langfuseStatus.hasHandler) {
      console.log('✅ Langfuse sessionId 与会话记录保持一致');
    } else {
      console.log('ℹ️  Langfuse 未启用，但会话上下文正常工作');
    }

    console.log('\n✅ CLI 会话上下文测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 清理资源
    await agent.close();
  }
}

// 运行测试
testCliSessionContext().catch(console.error);
