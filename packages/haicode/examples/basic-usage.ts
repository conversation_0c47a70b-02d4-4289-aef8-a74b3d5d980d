#!/usr/bin/env tsx

/**
 * Hai Code Agent 基本使用示例
 * 展示如何在代码中使用 HaiCodeAgent 并启用 Langfuse 追踪
 */

import { HaiCodeAgent } from '../src/index.js';

async function basicUsageExample() {
  console.log('🚀 Hai Code Agent 基本使用示例\n');

  // 创建 Agent 实例
  const agent = new HaiCodeAgent({
    model: 'ht::saas-deepseek-v3',
    baseUrl: process.env.OPENAI_BASE_URL,
    apiKey: process.env.OPENAI_API_KEY,
    langfuse: {
      enabled: true, // 启用 Langfuse 追踪
      userId: 'example-user',
      sessionId: `example-${Date.now()}`,
      version: 'example-1.0.0',
    },
  });

  // 检查 Langfuse 状态
  const status = agent.getLangfuseStatus();
  console.log(`📊 Langfuse 状态: ${status.enabled ? '启用' : '禁用'} (处理器: ${status.hasHandler ? '已创建' : '未创建'})\n`);

  try {
    // 示例 1: 简单对话
    console.log('💬 示例 1: 简单对话');
    console.log('用户: 什么是人工智能？');
    console.log('助手: ');
    
    for await (const chunk of agent.streamMessage('什么是人工智能？请用一句话简单回答')) {
      process.stdout.write(chunk);
    }
    console.log('\n');

    // 示例 2: 代码相关问题
    console.log('💻 示例 2: 代码相关问题');
    console.log('用户: 如何在 TypeScript 中定义一个接口？');
    console.log('助手: ');
    
    for await (const chunk of agent.streamMessage('如何在 TypeScript 中定义一个接口？请给出简单示例')) {
      process.stdout.write(chunk);
    }
    console.log('\n');

    // 示例 3: 工具使用
    console.log('🔧 示例 3: 工具使用');
    console.log('用户: 请帮我创建一个包含当前时间的文件');
    console.log('助手: ');
    
    for await (const chunk of agent.streamMessage('请帮我创建一个名为 current-time.txt 的文件，内容是当前的日期和时间')) {
      process.stdout.write(chunk);
    }
    console.log('\n');

    // 确保所有 Langfuse 事件都发送到服务器
    console.log('🔄 正在发送追踪数据到 Langfuse...');
    await agent.flushLangfuse();
    console.log('✅ 追踪数据已发送');

  } catch (error) {
    console.error('❌ 发生错误:', error);
    // 即使出错也尝试发送追踪数据
    await agent.flushLangfuse();
  }

  console.log('\n🎉 示例完成！');
  
  if (status.enabled && status.hasHandler) {
    console.log('📊 您可以在 Langfuse 控制台中查看详细的追踪数据');
  }
}

// 运行示例
basicUsageExample().catch((error) => {
  console.error('❌ 示例运行失败:', error);
  process.exit(1);
});
