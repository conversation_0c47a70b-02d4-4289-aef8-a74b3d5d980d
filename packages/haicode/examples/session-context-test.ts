import { HaiCodeAgent } from '../src/index.js';

async function testSessionContext() {
  console.log('🧪 测试会话上下文修复...\n');

  // 创建代理实例，启用会话持久化
  const agent = new HaiCodeAgent({
    enablePersistence: true,
    maxIterations: 10,
    systemPrompt: '你是一个有用的助手。请记住我们之前的对话内容。',
  });

  const sessionId = 'test-session-123';

  try {
    // 第一次对话
    console.log('📝 第一次对话:');
    const response1 = await agent.processMessage('你好，我的名字是张三', sessionId);
    console.log('AI回复:', response1);
    console.log('');

    // 检查会话状态
    console.log('🔍 检查会话状态:');
    const state1 = await agent.getSessionState(sessionId);
    console.log('会话状态:', state1 ? `消息数量: ${state1.messages.length}` : '无状态');
    console.log('');

    // 第二次对话 - 应该能记住之前的对话
    console.log('📝 第二次对话:');
    const response2 = await agent.processMessage('你还记得我的名字吗？', sessionId);
    console.log('AI回复:', response2);
    console.log('');

    // 再次检查会话状态
    console.log('🔍 再次检查会话状态:');
    const state2 = await agent.getSessionState(sessionId);
    console.log('会话状态:', state2 ? `消息数量: ${state2.messages.length}` : '无状态');
    console.log('');

    // 第三次对话 - 测试工具调用
    console.log('📝 第三次对话 (测试工具调用):');
    const response3 = await agent.processMessage('请列出当前目录的文件', sessionId);
    console.log('AI回复:', response3);
    console.log('');

    // 最终检查会话状态
    console.log('🔍 最终会话状态:');
    const finalState = await agent.getSessionState(sessionId);
    console.log('会话状态:', finalState ? `消息数量: ${finalState.messages.length}` : '无状态');
    
    if (finalState) {
      console.log('消息历史:');
      finalState.messages.forEach((msg, index) => {
        const role = msg.constructor.name;
        const content = typeof msg.content === 'string' ? msg.content.substring(0, 100) + '...' : '复杂内容';
        console.log(`  ${index + 1}. [${role}] ${content}`);
      });
    }

    console.log('\n✅ 会话上下文测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 清理资源
    await agent.close();
  }
}

// 运行测试
testSessionContext().catch(console.error);
