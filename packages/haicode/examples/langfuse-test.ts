#!/usr/bin/env tsx

/**
 * Langfuse 集成测试脚本
 * 
 * 使用方法:
 * 1. 配置环境变量 (参考 .env.example)
 * 2. 运行: npx tsx examples/langfuse-test.ts
 */

import { HaiCodeAgent } from '../src/index.js';
import { getLangfuseHelper } from '../src/utils/langfuseHelper.js';
import { getUserInfo } from '../src/utils/user.js';

async function testLangfuseIntegration() {
  console.log('🧪 Langfuse 集成测试\n');

  // 1. 检查 Langfuse 配置
  const langfuseHelper = getLangfuseHelper();
  const config = langfuseHelper.getConfig();
  
  console.log('📋 Langfuse 配置:');
  console.log(`  启用状态: ${config.enabled ? '✅ 已启用' : '❌ 已禁用'}`);
  console.log(`  服务器地址: ${config.baseUrl}`);
  console.log(`  公钥: ${config.publicKey ? '✅ 已配置' : '❌ 未配置'}`);
  console.log(`  私钥: ${config.secretKey ? '✅ 已配置' : '❌ 未配置'}`);
  console.log('');

  if (!config.enabled) {
    console.log('⚠️  Langfuse 未启用，设置 LANGFUSE_ENABLED=true 来启用');
    return;
  }

  if (!config.publicKey || !config.secretKey) {
    console.log('⚠️  Langfuse 凭据未配置，请设置 LANGFUSE_PUBLIC_KEY 和 LANGFUSE_SECRET_KEY');
    return;
  }

  // 2. 创建 Agent 实例
  const sessionId = `test-${Date.now()}`;
  const userInfo = getUserInfo();
  
  console.log('🤖 创建 Agent 实例:');
  console.log(`  会话ID: ${sessionId}`);
  console.log(`  用户: ${userInfo.userName}`);
  console.log('');

  const agent = new HaiCodeAgent({
    model: process.env.HAI_CODE_MODEL || 'ht::saas-deepseek-v3',
    langfuse: {
      enabled: true,
      userId: userInfo.userName,
      sessionId: sessionId,
      version: 'test-0.1.0',
    },
  });

  // 3. 检查 Agent 的 Langfuse 状态
  const status = agent.getLangfuseStatus();
  console.log('🔍 Agent Langfuse 状态:');
  console.log(`  Langfuse 启用: ${status.enabled ? '✅' : '❌'}`);
  console.log(`  回调处理器: ${status.hasHandler ? '✅ 已创建' : '❌ 未创建'}`);
  console.log('');

  if (!status.hasHandler) {
    console.log('❌ 回调处理器创建失败，请检查配置');
    return;
  }

  // 4. 测试简单对话
  console.log('💬 测试对话 (这将被 Langfuse 追踪):');
  console.log('用户: 你好，请简单介绍一下你自己');
  console.log('助手: ', { end: '' });

  try {
    let response = '';
    for await (const chunk of agent.streamMessage('你好，请简单介绍一下你自己')) {
      process.stdout.write(chunk);
      response += chunk;
    }
    console.log('\n');

    // 5. 测试工具调用
    console.log('🔧 测试工具调用 (这也将被 Langfuse 追踪):');
    console.log('用户: 请帮我创建一个简单的 hello.txt 文件，内容是 "Hello, Langfuse!"');
    console.log('助手: ', { end: '' });

    for await (const chunk of agent.streamMessage('请帮我创建一个简单的 hello.txt 文件，内容是 "Hello, Langfuse!"')) {
      process.stdout.write(chunk);
    }
    console.log('\n');

    // 6. 刷新 Langfuse 事件
    console.log('🔄 刷新 Langfuse 事件...');
    await agent.flushLangfuse();
    console.log('✅ 事件已发送到 Langfuse');

    // 7. 提供查看链接
    console.log('\n📊 查看追踪数据:');
    console.log(`  Langfuse 控制台: ${config.baseUrl}`);
    console.log(`  会话ID: ${sessionId}`);
    console.log(`  用户ID: ${userInfo.userName}`);

  } catch (error) {
    console.error('\n❌ 测试过程中出现错误:', error);
    await agent.flushLangfuse(); // 即使出错也尝试发送事件
  }

  console.log('\n🎉 Langfuse 集成测试完成!');
}

// 运行测试
testLangfuseIntegration().catch((error) => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
