/**
 * 示例：如何使用HaiAgent的系统提示词功能
 * 
 * 这个示例展示了：
 * 1. 使用默认系统提示词
 * 2. 自定义系统提示词
 * 3. 添加用户记忆
 * 4. 动态更新系统提示词
 */

import { HaiCodeAgent } from '../src/index.js';

async function systemPromptExample() {
  console.log('=== HaiCodeAgent 系统提示词示例 ===\n');

  // 1. 创建带有默认系统提示词的Agent
  console.log('1. 创建默认Agent...');
  const agent = new HaiCodeAgent({
    model: 'gpt-3.5-turbo',
    apiKey: process.env.OPENAI_API_KEY || 'your-api-key-here',
    temperature: 0.7
  });

  // 查看默认系统提示词
  console.log('默认系统提示词预览:');
  const defaultPrompt = agent.getCurrentSystemPrompt();
  console.log(defaultPrompt.substring(0, 200) + '...\n');

  // 2. 使用自定义系统提示词
  console.log('2. 设置自定义系统提示词...');
  const customPrompt = `
你是一个专业的代码审查助手。你的任务是：
- 分析代码质量和最佳实践
- 提供具体的改进建议
- 识别潜在的bug和安全问题
- 始终以中文回复，保持专业和友好的语调
`;

  agent.updateSystemPrompt(customPrompt);
  console.log('自定义系统提示词已设置\n');

  // 3. 添加用户记忆
  console.log('3. 添加用户记忆...');
  const userMemory = `
用户偏好：
- 喜欢使用TypeScript
- 偏好函数式编程风格
- 使用ESLint和Prettier进行代码格式化
- 项目主要使用React和Node.js
`;

  agent.updateUserMemory(userMemory);
  console.log('用户记忆已添加\n');

  // 4. 查看完整的系统提示词
  console.log('4. 查看完整的系统提示词...');
  const fullPrompt = agent.getCurrentSystemPrompt();
  console.log('完整系统提示词:');
  console.log(fullPrompt);
  console.log('\n');

  // 5. 测试Agent响应
  console.log('5. 测试Agent响应...');
  try {
    const response = await agent.processMessage('请帮我审查这段JavaScript代码：function add(a, b) { return a + b; }');
    console.log('Agent响应:', response);
  } catch (error) {
    console.log('注意：需要有效的API密钥才能测试实际响应');
    console.log('错误:', error instanceof Error ? error.message : String(error));
  }

  // 6. 重置为默认提示词
  console.log('\n6. 重置为默认提示词...');
  agent.resetSystemPrompt();
  console.log('已重置为默认系统提示词');

  // 7. 获取工具信息
  console.log('\n7. 查看可用工具...');
  const tools = agent.getToolsInfo();
  console.log('可用工具:');
  tools.forEach(tool => {
    console.log(`- ${tool.name}: ${tool.description}`);
  });

  console.log('\n=== 示例完成 ===');
}

// 运行示例
if (import.meta.url === `file://${process.argv[1]}`) {
  systemPromptExample().catch(console.error);
}

export { systemPromptExample };
