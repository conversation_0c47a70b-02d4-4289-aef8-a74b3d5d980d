#!/bin/bash

# Hai Code Agent CLI 演示脚本

echo "🚀 Hai Code Agent CLI 演示"
echo "========================"
echo ""

# 确保在正确的目录
cd "$(dirname "$0")/.."

# 检查是否已构建
if [ ! -f "dist/cli.js" ]; then
    echo "⚠️  请先构建项目: npm run build"
    exit 1
fi

echo "1. 显示帮助信息"
echo "命令: node dist/cli.js --help"
echo "---"
node dist/cli.js --help
echo ""

echo "2. 显示版本信息"
echo "命令: node dist/cli.js --version"
echo "---"
node dist/cli.js --version
echo ""

echo "3. 简单问答（非交互模式）"
echo "命令: node dist/cli.js -p \"什么是 JavaScript？\""
echo "---"
node dist/cli.js -p "什么是 JavaScript？"
echo ""

echo "4. 管道输入演示"
echo "命令: echo \"解释一下什么是递归\" | node dist/cli.js"
echo "---"
echo "解释一下什么是递归" | node dist/cli.js
echo ""

echo "5. 使用自定义模型"
echo "命令: node dist/cli.js -m \"ht::saas-deepseek-v3\" -p \"简单解释什么是 Docker\""
echo "---"
node dist/cli.js -m "ht::saas-deepseek-v3" -p "简单解释什么是 Docker"
echo ""

echo "6. 调试模式演示"
echo "命令: node dist/cli.js -d -p \"Hello\""
echo "---"
node dist/cli.js -d -p "Hello"
echo ""

echo "✅ 演示完成！"
echo ""
echo "💡 提示："
echo "- 使用 'node dist/cli.js -i' 启动交互模式"
echo "- 使用 'node dist/cli.js' 默认进入交互模式"
echo "- 在交互模式中输入 'exit' 或 'quit' 退出"
