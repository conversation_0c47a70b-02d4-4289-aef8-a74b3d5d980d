# HaiCode 日志系统优化

## 概述

本次优化对 packages/haicode 中的日志系统进行了全面重构，实现了符合最佳实践的日志记录功能。新的日志系统支持不同日志级别、调试模式控制、结构化日志输出等功能。

## 主要特性

### 1. 日志级别控制
- **ERROR**: 错误信息
- **WARN**: 警告信息  
- **INFO**: 一般信息
- **DEBUG**: 调试信息

### 2. 调试模式控制
- 只有在启用调试模式时，`debug()` 方法才会输出日志
- `debugAlways()` 方法始终输出（如果日志级别允许）
- 支持通过环境变量或代码控制调试模式

### 3. 结构化日志
- 支持上下文信息（Context）
- JSON 格式的结构化输出
- 时间戳和前缀标识

### 4. 颜色支持
- 不同日志级别使用不同颜色
- 支持禁用颜色输出
- 自动检测终端类型

## 环境变量配置

| 环境变量 | 说明 | 可选值 |
|---------|------|--------|
| `HAI_CODE_LOG_LEVEL` | 日志级别 | `error`, `warn`, `info`, `debug` |
| `HAI_CODE_DEBUG` | 启用调试模式 | `true`, `false`, `1`, `0`, `yes`, `no` |
| `HAI_CODE_COLORS` | 启用颜色输出 | `true`, `false`, `1`, `0`, `yes`, `no` |

## 使用方法

### 基本使用

```typescript
import { logger, enableDebugMode, disableDebugMode } from './utils/logger.js';

// 启用调试模式
enableDebugMode();

// 记录不同级别的日志
logger.error('发生错误', { errorCode: 500, userId: '123' });
logger.warn('警告信息', { warningType: 'deprecated' });
logger.info('一般信息', { action: 'user_login' });
logger.debug('调试信息', { debugData: 'some data' });

// 禁用调试模式
disableDebugMode();
```

### 创建子日志器

```typescript
import { logger } from './utils/logger.js';

// 创建带有前缀的子日志器
const mcpLogger = logger.child('MCPClient');
const langfuseLogger = logger.child('LangfuseHelper');

mcpLogger.info('MCP 客户端初始化成功', { serverCount: 2 });
langfuseLogger.debug('Langfuse 配置已更新');
```

### 自定义日志器

```typescript
import { createLogger, LogLevel } from './utils/logger.js';

const customLogger = createLogger({
  level: LogLevel.DEBUG,
  enableDebug: true,
  prefix: '[Custom]',
  enableColors: false,
});

customLogger.info('自定义日志器消息');
```

## 代码变更总结

### 新增文件
1. `src/utils/logger.ts` - 核心日志系统
2. `src/config/logger.ts` - 日志配置管理
3. `src/utils/logger.test.ts` - 日志系统测试
4. `.ht/01-日志系统优化.md` - 本文档

### 修改文件
1. `src/index.ts` - 替换 console.log 为结构化日志
2. `src/cli.ts` - 集成调试模式控制
3. `src/mcp/client.ts` - 使用子日志器
4. `src/utils/langfuseHelper.ts` - 使用子日志器
5. `src/utils/interactive.ts` - 错误日志优化
6. `src/utils/version.ts` - 警告日志优化

### 主要改进

#### 1. 结构化错误处理
```typescript
// 之前
console.error('刷新 Langfuse 事件失败:', error);

// 现在
logger.error('刷新 Langfuse 事件失败', { 
  error: error instanceof Error ? error.message : String(error) 
});
```

#### 2. 调试模式集成
```typescript
// 之前
if (options.debug) {
  console.log(chalk.gray('Debug mode enabled'));
  console.log(chalk.gray(`Model: ${options.model}`));
  // ...
}

// 现在
if (options.debug) {
  enableDebugMode();
  logger.debug('调试模式已启用');
  logger.debug('配置信息', {
    model: options.model,
    baseUrl: options.baseUrl || 'default',
    // ...
  });
}
```

#### 3. 子日志器使用
```typescript
// 之前
console.log(`MCP Client initialized with ${serverCount} servers`);

// 现在
const mcpLogger = logger.child('MCPClient');
mcpLogger.info('MCP 客户端初始化成功', { 
  serverCount, 
  toolCount: this.tools.length 
});
```

## 最佳实践

### 1. 日志级别选择
- **ERROR**: 系统错误、异常情况
- **WARN**: 潜在问题、降级处理
- **INFO**: 重要操作、状态变更
- **DEBUG**: 详细调试信息

### 2. 上下文信息
- 使用结构化对象传递上下文
- 包含相关的 ID、状态、参数等
- 避免敏感信息泄露

### 3. 性能考虑
- 调试日志只在调试模式启用时输出
- 避免在热路径中进行复杂的日志格式化
- 使用 `debug()` 而非 `debugAlways()` 用于调试信息

### 4. 错误处理
- 始终捕获并记录错误信息
- 使用 `error instanceof Error` 检查
- 提取错误消息而非整个错误对象

## 测试覆盖

新增的测试文件 `logger.test.ts` 覆盖了：
- 日志级别过滤
- 调试模式控制
- 结构化日志输出
- 子日志器功能
- 消息格式化
- 全局函数

## 向后兼容性

- 保持了原有的 API 接口
- 新增功能不影响现有代码
- 环境变量配置是可选的

## 总结

通过本次优化，HaiCode 的日志系统现在具备了：
- 更好的可观测性
- 更灵活的配置选项
- 更结构化的输出格式
- 更完善的调试支持
- 更符合最佳实践的实现

这些改进将有助于开发调试、问题排查和系统监控。
