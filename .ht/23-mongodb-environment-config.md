# MongoDB 环境配置自动切换功能

## 概述

本项目实现了 MongoDB checkpointer 配置的自动环境切换功能，可以根据运行环境自动选择相应的 MongoDB 配置。

## 功能特性

- **自动环境检测**：根据执行路径自动判断当前运行环境
- **配置自动切换**：开发环境使用本地 MongoDB，生产环境使用测试环境 MongoDB
- **向后兼容**：保持原有 API 不变，新增功能不影响现有代码

## 环境检测规则

### 开发环境 (使用 `CHECKPOINTER_CONFIG`)
以下情况会被识别为开发环境：

1. **环境变量**：`NODE_ENV=development`
2. **执行路径**：直接执行 `node packages/haicode/dist/cli.js`

### 生产环境 (使用 `CHECKPOINTER_CONFIG_SIT`)
以下情况会被识别为生产环境：

1. **环境变量**：`NODE_ENV=production`
2. **其他所有情况**：包括但不限于：
   - 通过 npm 包安装使用
   - 通过全局命令 `hai-code` 使用
   - 通过其他路径执行
   - 默认情况

## 配置详情

### 本地开发环境配置
```typescript
export const CHECKPOINTER_CONFIG = {
  uri: '***************************************************************************',
  dbName: 'haicode_cli',
  checkpointCollectionName: 'haicode_agent_checkpoints',
  checkpointWritesCollectionName: 'haicode_agent_checkpoint_writes'
};
```

### 测试环境配置
```typescript
export const CHECKPOINTER_CONFIG_SIT = {
  uri: '*********************************************************************',
  dbName: 'webdp',
  checkpointCollectionName: 'haicode_agent_checkpoints',
  checkpointWritesCollectionName: 'haicode_agent_checkpoint_writes'
};
```

## 使用方法

### 1. 自动配置（推荐）
```typescript
import { getCheckpointerConfig } from './config/mongo.js';

// 自动根据环境获取配置
const config = getCheckpointerConfig();
```

### 2. 手动指定环境
```typescript
// 设置环境变量
process.env.NODE_ENV = 'development'; // 或 'production'

// 然后使用自动配置
const config = getCheckpointerConfig();
```

### 3. 向后兼容
```typescript
// 仍然可以使用原有的配置常量
import { CHECKPOINTER_CONFIG, CHECKPOINTER_CONFIG_SIT } from './config/mongo.js';

// 或者使用当前环境的配置
import { CHECKPOINTER_CONFIG_CURRENT } from './config/mongo.js';
```

## 在项目中的使用

### haicode 包
在 `packages/haicode/src/config/mongo.ts` 中实现了环境检测功能。

### lang 包
在 `packages/lang/src/config/mongo.ts` 中实现了相同的环境检测功能，并在 `packages/lang/src/index.ts` 中使用。

## 测试

运行测试以验证环境检测功能：

```bash
# haicode 包测试
cd packages/haicode
npm test -- src/config/mongo.test.ts

# lang 包测试
cd packages/lang
npm test -- src/config/mongo.test.ts
```

## 环境变量说明

| 环境变量 | 值 | 说明 |
|---------|---|------|
| `NODE_ENV` | `development` | 强制使用开发环境配置 |
| `NODE_ENV` | `production` | 强制使用生产环境配置 |

## 使用场景示例

### 开发环境
```bash
# 在项目根目录下直接执行
node packages/haicode/dist/cli.js "Hello World"

# 或者设置环境变量
NODE_ENV=development node packages/haicode/dist/cli.js "Hello World"
```

### 生产环境
```bash
# 通过全局安装的命令
hai-code "Hello World"

# 通过 npm 包
npx @ht/hai-code-cli "Hello World"

# 或者设置环境变量
NODE_ENV=production node packages/haicode/dist/cli.js "Hello World"
```

## 注意事项

1. **优先级**：环境变量 `NODE_ENV` 具有最高优先级
2. **默认行为**：没有明确标识时默认为生产环境
3. **安全性**：生产环境配置包含敏感信息，请确保安全存储
4. **兼容性**：新功能完全向后兼容，不会破坏现有代码
5. **简化设计**：判断逻辑已简化，只检查 `packages/haicode/dist/cli.js` 路径

## 更新日志

- **v0.2.0-alpha.4**: 实现自动环境检测和配置切换功能
- 添加 `getCheckpointerConfig()` 函数
- 添加 `CHECKPOINTER_CONFIG_CURRENT` 常量
- 更新 lang 包以使用新的配置函数
- 添加完整的测试覆盖
- **最新更新**: 简化环境判断逻辑，只检查 `packages/haicode/dist/cli.js` 路径
