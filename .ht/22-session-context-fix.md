# 会话上下文修复报告

## 问题描述

`packages/haicode` 包在测试中发现丢失了会话上下文，导致 AI 无法记住之前的对话内容。同时，CLI 工具也没有传入 sessionId，导致每次调用都创建新的会话。

## 根本原因分析

1. **缺少 MemorySaver/Checkpointer**: 没有使用 LangGraph 的 MemorySaver 来持久化会话状态
2. **状态管理不完整**: 只定义了基本的 messages 状态，缺少 userMemory 和 iterationCount
3. **没有 thread_id 配置**: 缺少 LangGraph 所需的 thread_id 来标识会话
4. **状态初始化问题**: 每次调用都重新初始化状态，没有利用 LangGraph 的状态持久化
5. **CLI 缺少 sessionId**: CLI 和交互模式都没有传入 sessionId，导致会话上下文丢失
6. **Langfuse sessionId 不一致**: Langfuse 的 sessionId 与会话记录的 sessionId 不一致

## 修复方案

### 1. 添加完整的状态管理

参考 LangGraph 最佳实践，在 `StateAnnotation` 中添加了完整的状态定义：

```typescript
const StateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
  }),
});
```

### 2. 集成 MemorySaver

添加了 MemorySaver 来支持会话持久化：

```typescript
private initializeCheckpointer(): void {
  if (this.config.enablePersistence !== false) {
    this.checkpointer = new MemorySaver();
    logger.debug('[HaiCodeAgent] MemorySaver checkpointer initialized for session persistence');
  }
}
```

### 3. 添加 thread_id 配置

在调用工作流时添加了 thread_id 配置：

```typescript
const config: Record<string, unknown> = {
  streamMode: "messages",
  recursionLimit: this.maxIterations,
  configurable: {
    thread_id: threadId, // 这是 MemorySaver 所需的
  },
};
```

### 4. 改进状态初始化

修改了状态初始化逻辑，让 LangGraph MemorySaver 自动合并现有线程状态：

```typescript
const initialState = {
  messages: [new HumanMessage(message)],
  userMemory: this.config.userMemory || "",
  iterationCount: 0,
};
```

### 5. 添加会话管理方法

新增了会话状态管理方法：

- `getSessionState(sessionId: string)`: 获取当前会话状态
- `clearSessionState(sessionId: string)`: 清除会话状态

### 6. 修复 CLI 会话上下文

#### 6.1 添加 sessionId 参数支持

在 CLI 中添加了 `--session-id` 参数：

```typescript
export interface CliOptions {
  // ... 现有配置
  sessionId?: string;
}
```

#### 6.2 统一 sessionId 管理

确保 Langfuse 的 sessionId 与会话记录的 sessionId 保持一致：

```typescript
// 生成或使用提供的 sessionId
const sessionId = options.sessionId || process.env.HAI_CODE_SESSION_ID || getUUID();

// 创建代理实例时使用相同的 sessionId
const agent = new HaiCodeAgent({
  // ... 其他配置
  enablePersistence: true,
  langfuse: {
    enabled: langfuseConfig.enabled,
    userId: userInfo.userName,
    sessionId, // 使用相同的 sessionId 确保 Langfuse 和会话记录一致
    version: getVersion(),
  },
});
```

#### 6.3 更新所有调用点

更新了所有调用 `streamMessage` 和 `runInteractiveMode` 的地方，传入 sessionId：

```typescript
// CLI 命令模式
for await (const chunk of agent.streamMessage(prompt, sessionId)) {
  process.stdout.write(chunk);
}

// 交互模式
await runInteractiveMode(agent, sessionId);

// 交互模式内部
for await (const chunk of agent.streamMessage(trimmed, sessionId)) {
  process.stdout.write(chunk);
}
```

## 配置选项

在 `AgentConfig` 接口中添加了新的配置选项：

```typescript
export interface AgentConfig {
  // ... 现有配置
  enablePersistence?: boolean;  // 是否启用会话持久化
  maxIterations?: number;       // 最大迭代次数
}
```

在 CLI 中添加了新的参数：

```bash
-s, --session-id <id>   Session ID for conversation continuity
```

环境变量支持：

```bash
HAI_CODE_SESSION_ID     Default session ID for conversation continuity
```

## 测试验证

创建了多个测试文件来验证修复效果：

1. `packages/haicode/examples/session-context-test.ts` - 完整测试
2. `packages/haicode/examples/simple-session-test.ts` - 简单测试
3. `packages/haicode/examples/basic-session-test.ts` - 基本测试
4. `packages/haicode/examples/cli-session-test.ts` - CLI 测试

测试内容包括：
- 连续对话的记忆能力
- 会话状态的持久化
- 工具调用后的状态保持
- 消息历史的完整性
- Langfuse sessionId 一致性

## 使用方法

### 基本使用

```typescript
const agent = new HaiCodeAgent({
  enablePersistence: true,  // 启用会话持久化
  maxIterations: 10,
});

// 使用相同的 sessionId 来保持会话上下文
const sessionId = 'user-session-123';
const response1 = await agent.processMessage('你好，我是张三', sessionId);
const response2 = await agent.processMessage('你还记得我的名字吗？', sessionId);
```

### CLI 使用

```bash
# 基本使用
hai-code "What is the capital of France?"

# 使用 sessionId 保持会话上下文
hai-code -s my-session "Hello, my name is John"
hai-code -s my-session "Do you remember my name?"

# 使用环境变量设置默认 sessionId
export HAI_CODE_SESSION_ID=my-default-session
hai-code "Hello" && hai-code "Remember me?"

# 交互模式
hai-code -i -s my-session
```

### 会话状态管理

```typescript
// 获取会话状态
const state = await agent.getSessionState(sessionId);

// 清除会话状态
await agent.clearSessionState(sessionId);
```

## 参考文档

修复方案基于以下 LangGraph.js 最佳实践：

1. [LangGraph State Management](https://langchain-ai.github.io/langgraph/docs/tutorials/state/)
2. [Memory and Persistence](https://langchain-ai.github.io/langgraph/docs/tutorials/memory/)
3. [Checkpointing](https://langchain-ai.github.io/langgraph/docs/tutorials/checkpointing/)

## 兼容性说明

- 向后兼容：现有代码无需修改即可使用
- 默认行为：`enablePersistence` 默认为 `true`，保持现有行为
- 性能影响：MemorySaver 使用内存存储，性能影响最小
- CLI 兼容：新增的 `--session-id` 参数是可选的，不影响现有使用

## 修复效果

修复后的系统现在能够：

1. **保持会话上下文** - AI 能够记住之前的对话内容
2. **支持状态持久化** - 会话状态在多次调用间保持
3. **自动状态管理** - LangGraph MemorySaver 自动处理状态合并
4. **CLI 会话支持** - CLI 工具支持会话上下文保持
5. **Langfuse 一致性** - Langfuse 的 sessionId 与会话记录保持一致
6. **向后兼容** - 现有代码无需修改即可使用

## 后续优化建议

1. 考虑添加 MongoDB 等外部存储支持
2. 实现会话压缩以减少内存使用
3. 添加会话过期和清理机制
4. 支持会话迁移和备份功能
5. 添加会话统计和分析功能
6. 实现会话模板和预设功能
