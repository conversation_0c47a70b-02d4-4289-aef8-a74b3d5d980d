# HaiCode CLI 版本动态关联实现

## 概述

实现了 `packages/haicode` 通过 CLI 查看 version 获取的版本号与其 package.json 中的 version 动态关联。

## 实现内容

### 1. 创建版本工具函数

**文件**: `packages/haicode/src/utils/version.ts`

```typescript
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

/**
 * 获取当前包的版本号
 * @returns 版本号字符串
 */
export function getVersion(): string {
  try {
    // 获取当前文件的目录
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = dirname(__filename);
    
    // 从当前文件位置向上查找 package.json
    const packageJsonPath = join(__dirname, '..', '..', 'package.json');
    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
    
    return packageJson.version;
  } catch (error) {
    // 如果无法读取 package.json，返回默认版本
    console.warn('Warning: Could not read version from package.json, using default version');
    return '0.0.0';
  }
}
```

### 2. 修改 CLI 文件

**文件**: `packages/haicode/src/cli.ts`

#### 导入版本函数
```typescript
import { getVersion } from './utils/version.js';
```

#### 更新 printVersion 函数
```typescript
function printVersion() {
  const version = getVersion();
  console.log(`hai-code version ${version}`);
}
```

#### 更新 Langfuse 配置中的版本
```typescript
langfuse: {
  enabled: langfuseConfig.enabled,
  userId: userInfo.userName,
  sessionId,
  version: getVersion(), // 动态获取版本
},
```

### 3. 创建测试文件

**文件**: `packages/haicode/src/utils/version.test.ts`

```typescript
import { describe, it, expect } from 'vitest';
import { getVersion } from './version.js';

describe('getVersion', () => {
  it('should return a valid version string', () => {
    const version = getVersion();
    
    // 版本号应该是一个非空字符串
    expect(version).toBeTypeOf('string');
    expect(version.length).toBeGreaterThan(0);
    
    // 版本号应该符合语义化版本格式 (x.y.z 或 x.y.z-alpha.n)
    expect(version).toMatch(/^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$/);
  });

  it('should return the correct version from package.json', () => {
    const version = getVersion();
    
    // 当前 package.json 中的版本是 0.2.0-alpha.3
    expect(version).toBe('0.2.0-alpha.3');
  });
});
```

## 测试结果

### CLI 版本命令测试
```bash
$ node dist/cli.js --version
hai-code version 0.2.0-alpha.3
```

### 单元测试结果
```bash
$ npm test -- src/utils/version.test.ts --run
✓ src/utils/version.test.ts (2)
  ✓ getVersion (2)
    ✓ should return a valid version string
    ✓ should return the correct version from package.json
```

## 功能特点

1. **动态关联**: 版本号直接从 package.json 读取，无需手动维护
2. **错误处理**: 如果无法读取 package.json，会返回默认版本并显示警告
3. **多用途**: 不仅在 CLI 版本命令中使用，也在 Langfuse 配置中使用
4. **测试覆盖**: 包含完整的单元测试确保功能正确性

## 使用场景

1. **CLI 版本显示**: `hai-code --version` 命令
2. **Langfuse 可观测性**: 在遥测数据中包含正确的版本信息
3. **调试信息**: 在 debug 模式下显示版本信息

## 维护说明

- 当更新 package.json 中的 version 字段时，CLI 会自动使用新版本
- 无需修改代码，版本号会自动同步
- 建议在发布新版本时同时更新 package.json 的版本号
