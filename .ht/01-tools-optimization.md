# Haicode 工具优化报告

## 概述

本次优化针对 `packages/haicode/src/tools/` 下的工具实现，使其遵守 langchainjs 对 tool 的定义规范，并消除了 Zod 字段的报错。

## 问题描述

原始错误信息：
```
Zod field at `#/definitions/read_file/properties/offset` uses `.optional()` without `.nullable()` which is not supported by the API. See: https://platform.openai.com/docs/guides/structured-outputs?api-mode=responses#all-fields-must-be-required
This will become an error in a future version of the SDK.
```

## 根本原因

OpenAI API 要求所有字段都必须是必需的，不能使用 `.optional()` 而不使用 `.nullable()`。这是因为：
1. `.optional()` 使字段在 JSON 中完全不存在
2. OpenAI API 需要所有字段都存在，即使是 null 值
3. 使用 `.nullable()` 确保字段存在但可以为 null

## 优化内容

### 1. readFile.ts
**修改前：**
```typescript
schema: z.object({
  absolute_path: z.string().describe("..."),
  offset: z.number().optional().describe("..."),
  limit: z.number().optional().describe("..."),
})
```

**修改后：**
```typescript
schema: z.object({
  absolute_path: z.string().describe("..."),
  offset: z.number().nullable().describe("..."),
  limit: z.number().nullable().describe("..."),
})
```

**代码逻辑调整：**
- 将 `offset !== undefined` 改为 `offset !== null`
- 将 `limit !== undefined` 改为 `limit !== null`
- 使用 `offset ?? 0` 替代 `offset || 0`

### 2. grep.ts
**修改前：**
```typescript
schema: z.object({
  pattern: z.string().describe("..."),
  path: z.string().optional().describe("..."),
  include: z.string().optional().describe("..."),
})
```

**修改后：**
```typescript
schema: z.object({
  pattern: z.string().describe("..."),
  path: z.string().nullable().describe("..."),
  include: z.string().nullable().describe("..."),
})
```

### 3. glob.ts
**修改前：**
```typescript
schema: z.object({
  pattern: z.string().describe("..."),
  path: z.string().optional().describe("..."),
  case_sensitive: z.boolean().optional().describe("..."),
  respect_git_ignore: z.boolean().optional().describe("..."),
})
```

**修改后：**
```typescript
schema: z.object({
  pattern: z.string().describe("..."),
  path: z.string().nullable().describe("..."),
  case_sensitive: z.boolean().nullable().describe("..."),
  respect_git_ignore: z.boolean().nullable().describe("..."),
})
```

**代码逻辑调整：**
- 将 `case_sensitive !== undefined` 改为 `case_sensitive !== null`

### 4. shell.ts
**修改前：**
```typescript
schema: z.object({
  command: z.string().describe("..."),
  description: z.string().optional().describe("..."),
  directory: z.string().optional().describe("..."),
})
```

**修改后：**
```typescript
schema: z.object({
  command: z.string().describe("..."),
  description: z.string().nullable().describe("..."),
  directory: z.string().nullable().describe("..."),
})
```

### 5. edit.ts
**修改前：**
```typescript
schema: z.object({
  file_path: z.string().describe("..."),
  old_string: z.string().describe("..."),
  new_string: z.string().describe("..."),
  expected_replacements: z.number().min(1).optional().describe("..."),
})
```

**修改后：**
```typescript
schema: z.object({
  file_path: z.string().describe("..."),
  old_string: z.string().describe("..."),
  new_string: z.string().describe("..."),
  expected_replacements: z.number().min(1).nullable().describe("..."),
})
```

**接口类型调整：**
- 将 `EditToolParams` 接口中的 `expected_replacements?: number` 改为 `expected_replacements: number | null`
- 更新验证逻辑：将 `params.expected_replacements !== undefined` 改为 `params.expected_replacements !== null`

## 未修改的工具

以下工具不需要修改，因为它们没有使用 `.optional()` 字段：
- `writeFile.ts` - 所有字段都是必需的
- `webSearch.ts` - 所有字段都是必需的  
- `memoryTool.ts` - 所有字段都是必需的

## 优化效果

1. **消除 Zod 警告**：所有工具现在都符合 OpenAI API 的要求
2. **保持向后兼容**：功能逻辑保持不变，只是参数传递方式从 undefined 改为 null
3. **类型安全**：TypeScript 编译通过，没有类型错误
4. **符合规范**：完全遵守 langchainjs 对 tool 的定义规范

## 使用说明

对于工具使用者，主要变化是：
- 原来不传递可选参数（undefined），现在需要传递 null
- 例如：`{ pattern: "test", path: null, include: null }` 而不是 `{ pattern: "test" }`

## 验证结果

- ✅ TypeScript 编译通过
- ✅ 所有工具 schema 符合 OpenAI API 要求
- ✅ 功能逻辑保持不变
- ✅ 类型定义正确

## 后续建议

1. 在文档中明确说明参数传递方式的变化
2. 考虑添加参数验证的单元测试
3. 监控工具在实际使用中的表现
